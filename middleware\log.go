package middleware

import (
	"log"
	"runtime"
)

// LogError 说明：callerlevel 定位调用者级别
func LogError(err error, callerlevel int) {
	if err == nil {
		//return ""
		return
	}
	_, file, line, ok := runtime.Caller(callerlevel) // Skip 1 to get caller's info
	if !ok {
		log.Printf("failed to get caller info: %v", err)
		//return fmt.Sprintf("failed to get caller info: %v", err)
	}
	log.Printf("exception in %s:%d: %v", file, line, err)
	//return fmt.Sprintf("error in %s:%d: %v", file, line, err)
}
