package recog

import (
	"fmt"
	"imgProcServe/cgo"
	"imgProcServe/job"
	"imgProcServe/middleware"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

// createFileService 创建文件服务实例
func createFileService(imgUpDownLoadHeader string) (middleware.FileService, error) {
	// 获取MinIO配置
	minioConfig := job.GetMinIOConfig()

	// 根据文件服务类型创建相应的服务
	return middleware.CreateFileServiceFromSubjectConfig(
		imgUpDownLoadHeader,
		job.FileStoreType,
		minioConfig,
	)
}

type Task struct {
	TaskError          error
	SubjectId          int         `json:"subjectId"`
	CandidateNo        string      `json:"candidateNo"`
	Version            int         `json:"version"`
	OrgId              int         `json:"orgId"`
	RoomId             int         `json:"roomId"`
	PaperType          string      `json:"paperType"`
	RoomEncode         string      `json:"roomEncode"`
	ScanBatchNO        string      `json:"scanBatchNO"`
	AbnormalCardFlag   string      `json:"abnormalCardFlag"`
	ImageList          []TaskImage `json:"imgList"`
	OptionalSubjectOmr string      //主观题选做OMR串
	RecogTmpDir        string
	SubjectInfo        SubjectInfo
	ImageDownLoad      []ImageDownLoad
}

type TaskImage struct {
	FImage   string `json:"fImage"`
	BImage   string `json:"bImage"`
	ImageId  string `json:"imageId"`
	CardType string `json:"cardType"`
}

func getRecogTask(subjectId int) ([]*Task, error) {

	subjectInfo, err := getRecogSubject(subjectId)

	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectInfo[0].SubjectId)
	params["recordNum"] = strconv.Itoa(30) //每次取任务数量
	params["queryType"] = strconv.Itoa(0)  //未识别状态

	// 创建随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	recogTmpDirString := r.Intn(1000000)

	recogTaskList, err := job.FetchData[[]*Task, []*Task](
		job.NewHttpClient(),
		job.POST,
		"/omr/getOmrUnHandleCandidateList",
		params,
		nil)

	if err != nil {
		return nil, err
	}

	for _, task := range recogTaskList {
		task.RecogTmpDir = subjectInfo[0].SubRecogTempDir + "\\img\\" + strconv.Itoa(recogTmpDirString)
		task.SubjectInfo = *subjectInfo[0]

		params = make(map[string]string)
		params["subjectId"] = strconv.Itoa(task.SubjectId)
		params["candidateNo"] = task.CandidateNo
		imgList, err := job.FetchData[[]TaskImage, []TaskImage](
			job.NewHttpClient(),
			job.POST,
			"/cut/image/list",
			params,
			nil)

		if err != nil {
			return nil, err
		}
		if len(imgList) == 0 {
			task.TaskError = fmt.Errorf("该考生图像列表为空！")
			return nil, nil
		}
		task.ImageList = imgList

		imageCnt := len(imgList) * 2
		taskImageDownLoadList := make([]ImageDownLoad, 0, imageCnt)
		for _, image := range imgList {
			// 前面图像
			var fImgDownLoad ImageDownLoad
			fImgDownLoad.CardType = image.CardType
			fImgDownLoad.PreHttpPath = task.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
			fImgDownLoad.ImageHttpPath = image.FImage
			fImgDownLoad.ImageSourcePath = task.RecogTmpDir +
				"\\" + strings.ReplaceAll(image.FImage, "/", "\\")
			taskImageDownLoadList = append(taskImageDownLoadList, fImgDownLoad)

			// 背面图像
			if image.BImage != "" {
				var bImgDownLoad ImageDownLoad
				bImgDownLoad.CardType = image.CardType
				bImgDownLoad.PreHttpPath = task.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
				bImgDownLoad.ImageHttpPath = image.BImage
				bImgDownLoad.ImageSourcePath = task.RecogTmpDir +
					"\\" + strings.ReplaceAll(image.BImage, "/", "\\")
				taskImageDownLoadList = append(taskImageDownLoadList, bImgDownLoad)
			}
		}
		task.ImageDownLoad = taskImageDownLoadList
	}

	return recogTaskList, nil
}

// Recog 识别处理流程
func Recog(subjectId int) error {
	getTaskErrCnt := 0

	for {
		// 获取识别任务
		recogTaskList, err := getRecogTask(subjectId)
		if err != nil {
			fmt.Printf("获取识别数据失败:%v\n", err)
			getTaskErrCnt++
			if getTaskErrCnt > 5 {
				break
			} else {
				time.Sleep(time.Second * 2)
				continue
			}
		}

		if len(recogTaskList) == 0 {
			break
		}

		fmt.Printf("科目 %d 获取到 %d 个识别任务\n", subjectId, len(recogTaskList))
		for _, task := range recogTaskList {
			fmt.Printf("识别任务: 考生号=%s, 科目ID=%d\n", task.CandidateNo, task.SubjectId)
		}

		// 初始化模型
		if len(recogTaskList) > 0 {
			err = initModel(&recogTaskList[0].SubjectInfo)
			if err != nil {
				fmt.Printf("初始化识别模型失败: %v\n", err)
				continue
			}
		}

		// 处理识别任务
		err = processRecogTasks(recogTaskList)
		if err != nil {
			fmt.Printf("处理识别任务失败: %v\n", err)
		}
	}
	return nil
}

// processRecogTasks 处理识别任务列表
func processRecogTasks(taskList []*Task) error {
	if len(taskList) == 0 {
		return nil
	}

	// 使用管道控制任务流程
	taskChan := make(chan *Task, len(taskList))
	downloadChan := make(chan *Task, len(taskList))
	recognizeChan := make(chan *Task, len(taskList))
	updateChan := make(chan *Task, len(taskList))

	// 将任务放入管道
	for _, task := range taskList {
		taskChan <- task
	}
	close(taskChan)

	var wg sync.WaitGroup

	// 阶段1: 多协程下载图像
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(downloadChan)

		var downloadWg sync.WaitGroup
		// 限制并发下载数量
		semaphore := make(chan struct{}, 3)

		for task := range taskChan {
			downloadWg.Add(1)
			go func(t *Task) {
				defer downloadWg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				if t.TaskError == nil {
					t.downloadImages()
				}
				downloadChan <- t
			}(task)
		}
		downloadWg.Wait()
	}()

	// 阶段2: 多协程识别处理
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(recognizeChan)

		var recognizeWg sync.WaitGroup
		// 限制并发识别数量
		semaphore := make(chan struct{}, 5)

		for task := range downloadChan {
			recognizeWg.Add(1)
			go func(t *Task) {
				defer recognizeWg.Done()
				semaphore <- struct{}{}
				defer func() { <-semaphore }()

				if t.TaskError == nil {
					t.recognizeImages()
				}
				recognizeChan <- t
			}(task)
		}
		recognizeWg.Wait()
	}()

	// 阶段3: 单协程更新状态
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(updateChan)

		for task := range recognizeChan {
			// 更新识别结果
			err := task.updateRecogResult()
			if err != nil {
				fmt.Printf("更新识别结果失败: %v\n", err)
			}
			updateChan <- task
		}
	}()

	// 等待所有阶段完成
	wg.Wait()

	// 清理临时目录
	if len(taskList) > 0 {
		err := os.RemoveAll(taskList[0].RecogTmpDir)
		if err != nil {
			fmt.Printf("删除临时目录失败: %v\n", err)
		}
	}

	return nil
}

// downloadImages 下载图像
func (t *Task) downloadImages() {
	// 创建临时目录
	err := os.MkdirAll(t.RecogTmpDir, 0755)
	if err != nil {
		t.TaskError = fmt.Errorf("创建临时目录失败: %v", err)
		return
	}

	// 下载图像文件
	fileService, err := createFileService(t.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader)
	if err != nil {
		t.TaskError = fmt.Errorf("创建文件服务失败: %v", err)
		return
	}

	for _, downImg := range t.ImageDownLoad {
		err := fileService.DownloadFile(downImg.PreHttpPath+downImg.ImageHttpPath, downImg.ImageSourcePath)
		if err != nil {
			t.TaskError = fmt.Errorf("图像下载失败: %v", err)
			return
		}
	}
}

// recognizeImages 识别图像
func (t *Task) recognizeImages() {
	// 对每个图像进行识别
	for _, image := range t.ImageList {
		frontPath := t.RecogTmpDir + "\\" + strings.ReplaceAll(image.FImage, "/", "\\")
		backPath := t.RecogTmpDir + "\\" + strings.ReplaceAll(image.BImage, "/", "\\")

		// 调用识别函数
		_, err := cgo.RecogniseCard(frontPath, backPath)
		if err != nil {
			t.TaskError = fmt.Errorf("图像识别失败: %v", err)
			return
		}
	}
}

// updateRecogResult 更新识别结果
func (t *Task) updateRecogResult() error {
	// 这里应该调用API更新识别结果到数据库
	// 暂时返回nil，具体实现需要根据API接口
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(t.SubjectId)
	params["candidateNo"] = t.CandidateNo

	if t.TaskError != nil {
		params["status"] = "failed"
		params["errorMsg"] = t.TaskError.Error()
	} else {
		params["status"] = "success"
	}

	httpClient := job.NewHttpClient()
	_, err := job.FetchData[string, string](
		httpClient,
		job.POST,
		"/omr/updateRecogResult",
		params,
		nil)

	if err != nil {
		return fmt.Errorf("更新识别结果失败: %v", err)
	}

	if httpClient.HttpClient.Code != 200 {
		return fmt.Errorf("更新识别结果失败: %d %s", httpClient.HttpClient.Code, httpClient.HttpClient.Msg)
	}

	return nil
}
