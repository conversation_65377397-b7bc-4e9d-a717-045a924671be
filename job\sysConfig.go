package job

import (
	"fmt"
	"imgProcServe/middleware"
	"net/url"
	"strings"
)

// XrConfig 参数
type XrConfig struct {
	PId         int    `json:"pId"`
	ConfigKey   string `json:"configKey"`
	ConfigName  string `json:"configName"`
	ConfigValue string `json:"configValue"`
}

var DBID string            //扫描图像数据库标识ID
var FileStoreType string   //文件服务器类型 oss:阿里oss;minio:minio;loc:本地
var OSSCutImageFlag bool   //是否启用oss在线切割
var ScanImgEndPoint string //扫描图像文件服务地址
var PjImgEndPoint string   //评卷图像文件服务地址
var ScanImgPort string     //扫描图像文件服务端口
var PjImgEndPort string    //评卷图像文件服务端口

// MinIO配置参数
var MinIOEndpoint string   //MinIO服务器地址
var MinIOAccessKey string  //MinIO访问密钥
var MinIOSecretKey string  //MinIO秘密密钥
var MinIOBucketName string //MinIO存储桶名称
var MinIOUseSSL bool       //MinIO是否使用SSL

// 获取公共参数
func getSysConfig() error {
	configs := []XrConfig{
		{PId: 0, ConfigKey: "scan.exam.dbid"},
		{PId: 0, ConfigKey: "file.store.type"},
		{PId: 0, ConfigKey: "oss.cut.img"},
		{PId: 0, ConfigKey: "scan.subject.ScanImgFTPIP"},
		{PId: 0, ConfigKey: "scan.subject.PjImgFTPIP"},
		{PId: 0, ConfigKey: "scan.img.port"},
		{PId: 0, ConfigKey: "pj.img.port"},
		{PId: 0, ConfigKey: "img.buck.name"},
	}
	ConfigArr, err := FetchData[[]XrConfig, []XrConfig](
		NewHttpClient(),
		PostBody,
		"/system/config/getConfigs/byPIdKeyList",
		nil,
		configs)

	if err != nil {
		return err
	}

	for _, v := range ConfigArr {
		if v.ConfigValue == "" || v.ConfigValue == "-" {
			middleware.LogError(fmt.Errorf("警告：公共参数'%s'未设置", v.ConfigName+"-"+v.ConfigKey), 2)
			return fmt.Errorf("警告：公共参数'%s'未设置", v.ConfigName+"-"+v.ConfigKey)
		}

		switch v.ConfigKey {
		case "scan.exam.dbid":
			DBID = v.ConfigValue
		case "file.store.type":
			FileStoreType = v.ConfigValue
		case "oss.cut.img":
			value, err := middleware.StringToBool(v.ConfigValue)
			if err != nil {
				middleware.LogError(fmt.Errorf("警告：公共参数'%s'设置格式错误", v.ConfigName+"-"+v.ConfigKey), 2)
				return fmt.Errorf("警告：公共参数'%s'设置格式错误", v.ConfigName+"-"+v.ConfigKey)
			} else {
				OSSCutImageFlag = value
			}
		case "scan.subject.ScanImgFTPIP":
			ScanImgEndPoint = v.ConfigValue
		case "scan.subject.PjImgFTPIP":
			PjImgEndPoint = v.ConfigValue
		case "scan.img.port":
			ScanImgPort = v.ConfigValue
		case "pj.img.port":
			PjImgEndPort = v.ConfigValue
		case "img.buck.name":
			MinIOBucketName = v.ConfigValue
		}
	}
	if FileStoreType == "minio" {
		if ScanImgEndPoint != PjImgEndPoint {
			middleware.LogError(fmt.Errorf("警告：扫描图像服务地址和评卷图像服务地址不一致"), 2)
			return fmt.Errorf("警告：扫描图像服务地址和评卷图像服务地址不一致")
		}

		if ScanImgPort != PjImgEndPort {
			middleware.LogError(fmt.Errorf("警告：扫描图像服务端口和评卷图像服务端口不一致"), 2)
			return fmt.Errorf("警告：扫描图像服务端口和评卷图像服务端口不一致")
		}

		MinIOEndpoint = strings.TrimSpace(ScanImgEndPoint) + ":" + strings.TrimSpace(ScanImgPort)
		if strings.Contains(MinIOEndpoint, "https") {
			MinIOUseSSL = true
		} else {
			MinIOUseSSL = false
		}
		u, err := url.Parse(MinIOEndpoint)
		if err != nil {
			middleware.LogError(fmt.Errorf("警告：MinIO服务地址格式错误"), 2)
			return fmt.Errorf("警告：MinIO服务地址格式错误")
		}
		MinIOEndpoint = u.Host
		MinIOAccessKey = "hytadmin"
		MinIOSecretKey = "sky@2023"

	}

	return nil
}

// GetMinIOConfig 获取MinIO配置
func GetMinIOConfig() *middleware.MinIOConfig {
	if FileStoreType != "minio" {
		return nil
	}

	return &middleware.MinIOConfig{
		Endpoint:   MinIOEndpoint,
		AccessKey:  MinIOAccessKey,
		SecretKey:  MinIOSecretKey,
		BucketName: MinIOBucketName,
		UseSSL:     MinIOUseSSL,
	}
}
