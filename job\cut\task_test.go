package cut

import (
	"fmt"
	"imgProcServe/job"
	"log"
	"testing"
)

func TestProcJob_GetCutTask(t *testing.T) {
	err := job.Login()
	if err != nil {
		return
	}

	if cuttasklist, err := GetCutTask(1004522); err == nil {
		for _, task := range cuttasklist {
			fmt.Printf("%+v\n", task)
		}
	}
}

func TestStringConvertOcclusionArea(t *testing.T) {
	log.Print(stringConvertOcclusionArea("12,13,14,15;23,24,25,26;36,37,38,39"))
	log.Print(stringConvertOcclusionArea("12,13,14,15"))
}

func TestCutJob_GetItemCutRecord(t *testing.T) {
	err := job.Login()
	if err != nil {
		return
	}
	recordList, err := getItemCutRecord(1004522, "226000022002")
	if err != nil {
		log.Print(err)
	} else {
		fmt.Printf("%+v\n", recordList)
	}
}

func TestJob_Cut(t *testing.T) {
	err := job.Login()
	if err != nil {
		log.Print(err)
	}

	// 创建优先级信号用于测试
	prioritySignal := &PrioritySignal{
		HasPriority: make(chan bool, 1),
		Stop:        make(chan bool, 1),
	}

	if err := Cut(1008044, prioritySignal); err != nil {
		log.Print(err)
	}
}
