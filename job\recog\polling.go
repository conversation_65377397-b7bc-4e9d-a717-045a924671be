package recog

import (
	"fmt"
	"imgProcServe/job"
	"sync"
	"time"
)

// RecogPollingManager 识别轮询管理器
type RecogPollingManager struct {
	processingSubjects map[int]bool // 正在处理的科目ID
	mutex              sync.RWMutex
	stopChan           chan bool
	running            bool
}

// NewRecogPollingManager 创建新的识别轮询管理器
func NewRecogPollingManager() *RecogPollingManager {
	return &RecogPollingManager{
		processingSubjects: make(map[int]bool),
		stopChan:           make(chan bool, 1),
		running:            false,
	}
}

// Start 启动识别轮询机制
func (rpm *RecogPollingManager) Start() error {
	if rpm.running {
		return fmt.Errorf("识别轮询管理器已经在运行")
	}

	rpm.running = true
	fmt.Println("启动考试识别轮询机制...")

	// 主轮询逻辑
	go rpm.mainPolling()

	return nil
}

// Stop 停止识别轮询机制
func (rpm *RecogPollingManager) Stop() {
	if !rpm.running {
		return
	}

	fmt.Println("停止考试识别轮询机制...")
	rpm.running = false
	rpm.stopChan <- true
}

// mainPolling 主轮询逻辑
func (rpm *RecogPollingManager) mainPolling() {
	for rpm.running {
		select {
		case <-rpm.stopChan:
			fmt.Println("识别轮询协程退出")
			return
		default:
		}

		// 获取考试列表
		exams, err := job.GetExamList()
		if err != nil {
			fmt.Printf("获取考试列表失败: %v\n", err)
			time.Sleep(10 * time.Second)
			continue
		}

		if len(exams) == 0 {
			fmt.Println("没有待识别的考试，等待10秒后重新检查...")
			time.Sleep(10 * time.Second)
			continue
		}

		fmt.Printf("识别任务: 处理 %d 个考试\n", len(exams))
		rpm.processExams(exams)

		// 处理完一轮后稍作休息，与切割轮询错开时间
		time.Sleep(4 * time.Second)
	}
}

// processExams 处理考试列表
func (rpm *RecogPollingManager) processExams(exams []job.Exam) {
	for _, exam := range exams {
		if !rpm.running {
			return
		}

		fmt.Printf("开始处理识别任务考试: %s (ID: %d)\n", exam.ExamName, exam.ExamId)

		// 获取考试的科目列表
		subjects, err := getRecogSubjectList(exam.ExamId)
		if err != nil {
			fmt.Printf("获取考试 %d 的识别科目列表失败: %v\n", exam.ExamId, err)
			continue
		}

		if len(subjects) == 0 {
			fmt.Printf("考试 %d 没有可识别的科目\n", exam.ExamId)
			continue
		}

		fmt.Printf("考试 %d 有 %d 个可识别的科目\n", exam.ExamId, len(subjects))

		// 并发处理科目，限制并发数为3
		var subjectWg sync.WaitGroup
		semaphore := make(chan struct{}, 3) // 最多3个科目并发处理

		for _, subject := range subjects {
			if !rpm.running {
				break
			}

			subjectWg.Add(1)
			go func(subjectId int) {
				defer subjectWg.Done()
				semaphore <- struct{}{}        // 获取信号量
				defer func() { <-semaphore }() // 释放信号量

				//rpm.processSubject(subjectId)
			}(subject.SubjectId)
		}

		// 等待当前考试的所有科目处理完成
		subjectWg.Wait()
	}
}

// processSubject 处理单个科目
func (rpm *RecogPollingManager) processSubject(subjectId int) {
	// 检查是否已在处理中
	rpm.mutex.Lock()
	if rpm.processingSubjects[subjectId] {
		rpm.mutex.Unlock()
		fmt.Printf("科目 %d 正在识别处理中，跳过\n", subjectId)
		return
	}
	rpm.processingSubjects[subjectId] = true
	rpm.mutex.Unlock()

	// 处理完成后清理
	defer func() {
		rpm.mutex.Lock()
		delete(rpm.processingSubjects, subjectId)
		rpm.mutex.Unlock()
		fmt.Printf("科目 %d 识别处理完成\n", subjectId)
	}()

	fmt.Printf("开始识别处理科目 %d\n", subjectId)

	// 调用Recog函数处理科目
	err := Recog(subjectId)
	if err != nil {
		fmt.Printf("科目 %d 识别处理失败: %v\n", subjectId, err)
	}
}

// GetStatus 获取识别轮询管理器状态
func (rpm *RecogPollingManager) GetStatus() map[string]interface{} {
	rpm.mutex.RLock()
	defer rpm.mutex.RUnlock()

	processingSubjects := make([]int, 0, len(rpm.processingSubjects))
	for subjectId := range rpm.processingSubjects {
		processingSubjects = append(processingSubjects, subjectId)
	}

	return map[string]interface{}{
		"running":             rpm.running,
		"processing_subjects": processingSubjects,
		"subject_count":       len(rpm.processingSubjects),
	}
}

// IsRunning 检查是否正在运行
func (rpm *RecogPollingManager) IsRunning() bool {
	rpm.mutex.RLock()
	defer rpm.mutex.RUnlock()
	return rpm.running
}

// GetProcessingSubjects 获取正在处理的科目列表
func (rpm *RecogPollingManager) GetProcessingSubjects() []int {
	rpm.mutex.RLock()
	defer rpm.mutex.RUnlock()

	subjects := make([]int, 0, len(rpm.processingSubjects))
	for subjectId := range rpm.processingSubjects {
		subjects = append(subjects, subjectId)
	}
	return subjects
}
