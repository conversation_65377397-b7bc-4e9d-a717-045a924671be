package middleware

import (
	"github.com/go-resty/resty/v2"
	"time"
)

type RestyClient struct {
	Url      string
	Msg      string
	Total    int
	Code     int
	Response *resty.Response
	Client   *resty.Client
	Token    string
}

func (client *RestyClient) NewClient() {
	client.Client = resty.New()
	client.Client.SetRetryCount(1).SetRetryWaitTime(1 * time.Second)
}

func (client *RestyClient) SetToken(t string) {
	client.Token = t
}

func (client *RestyClient) Get(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").SetHeader("Authorization", client.Token).SetHeader("ClientType", "app").Get(url)
}

func (client *RestyClient) GetNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").Get(url)
}

func (client *RestyClient) Post(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").SetHeader("Authorization", client.Token).SetHeader("ClientType", "app").Post(url)
}

func (client *RestyClient) PostNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").Post(url)
}

func (client *RestyClient) PostBody(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetBody(p).SetHeader("Accept", "application/json").SetHeader("Authorization", client.Token).SetHeader("ClientType", "app").Post(url)
}

func (client *RestyClient) PostBodyList(url string, p interface{}) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetBody(p).SetHeader("Accept", "application/json").
		SetHeader("Content-Type", "application/json").SetHeader("Authorization", client.Token).
		SetHeader("ClientType", "app").Post(url)
}

func (client *RestyClient) PostBodyNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = client.Url + url
	return client.Client.R().SetBody(p).SetHeader("Accept", "application/json").Post(url)
}
