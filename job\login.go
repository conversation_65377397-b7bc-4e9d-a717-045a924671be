package job

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"imgProcServe/internal/config"
	"imgProcServe/middleware"
	"log"
)

type Client struct {
	Ip             string
	HttpClient     *middleware.RestyClient
	FileServerType string //文件服务器类型  oss:阿里oss;minio:minio;
}

// LoginInfo auto/login/safe 接收json
type LoginInfo struct {
	Msg      string   `json:"msg"`
	Code     int      `json:"code"`
	UserInfo UserData `json:"data"`
}

// UserData 用户信息
type UserData struct {
	UserId          int    `json:"userId"`
	UserName        string `json:"userName"`
	NickName        string `json:"nickName"`
	LoginIp         string `json:"loginIp"`
	LoginDate       string `json:"loginDate"`
	LoginErrorCount int    `json:"loginErrorCount"`
	ClientId        int    `json:"clientId"`
	OrgId           int    `json:"orgId"`
	UserType        int    `json:"userType"`
	Token           string `json:"token"`
}

var LoginUser LoginInfo

func GetUser(client middleware.RestyClient) (LoginInfo, error) {
	var userName = "cutadmin"
	var roleKey = "cut_user"
	var verifyCode = "1234"
	var clientType = "app"
	hash := md5.Sum([]byte(userName))
	var password = hex.EncodeToString(hash[:])

	params := map[string]string{}
	params["clientType"] = clientType
	params["password"] = password
	params["roleKey"] = roleKey
	params["verifyCode"] = verifyCode
	params["userName"] = userName

	var loginUser LoginInfo
	result, err := client.PostBodyNoToken("/auto/login/safe", params)
	if err != nil {
		log.Println(err)
		return loginUser, err
	}

	err = json.Unmarshal(result.Body(), &loginUser)
	if err != nil {
		log.Println(err)
		return loginUser, err
	}
	return loginUser, nil
}

func Login() error {

	err := config.LoadConfig()
	if err != nil {
		return fmt.Errorf("获取本地参数失败: %v", err)
	}
	serverUrl := config.BaseConfig.ServerUrl
	httpClient := middleware.RestyClient{
		Url: serverUrl + "/api",
	}
	httpClient.NewClient()
	LoginUser, err = GetUser(httpClient)
	if err != nil {
		return fmt.Errorf("获取用户失败: %v", err)
	}

	err = getSysConfig()
	if err != nil {
		return fmt.Errorf("获取系统参数失败: %v", err)
	}

	return nil
}

func NewHttpClient() *Client {
	httpClient := middleware.RestyClient{
		Url: config.BaseConfig.ServerUrl + "/api",
	}
	httpClient.NewClient()

	client := &Client{
		Ip:         config.BaseConfig.ServerUrl,
		HttpClient: &httpClient,
	}
	client.HttpClient.SetToken(LoginUser.UserInfo.Token)
	return client
}
