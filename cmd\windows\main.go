package main

import (
	"fmt"
	"imgProcServe/internal/config"
	"imgProcServe/job"
	"imgProcServe/job/cut"
	"imgProcServe/job/recog"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
)

func main() {
	fmt.Println("=== 图像处理服务 - 考试切割和识别轮询系统 ===")
	// 初始化登录
	fmt.Println("正在登录系统...")
	err := job.Login()
	if err != nil {
		log.Fatalf("登录失败: %v", err)
	}
	fmt.Println("登录成功")

	// 读取轮询开关配置
	cutEnabled := config.BaseConfig.IniConfig.CutPollingEnabled
	recogEnabled := config.BaseConfig.IniConfig.RecogPollingEnabled

	fmt.Printf("轮询配置: 切割轮询=%v, 识别轮询=%v\n", cutEnabled, recogEnabled)

	var cutPollingManager *cut.PollingManager
	var recogPollingManager *recog.RecogPollingManager

	// 根据配置创建轮询管理器
	if cutEnabled {
		cutPollingManager = cut.NewPollingManager()
	}
	if recogEnabled {
		recogPollingManager = recog.NewRecogPollingManager()
	}

	// 启动确认通道
	cutStarted := make(chan bool, 1)
	recogStarted := make(chan bool, 1)

	fmt.Println("正在启动轮询系统...")

	// 启动切割轮询机制
	if cutEnabled {
		go func() {
			err := cutPollingManager.Start()
			if err != nil {
				log.Printf("启动切割轮询机制失败: %v", err)
				cutStarted <- false
			} else {
				fmt.Println("✓ 切割轮询启动成功")
				cutStarted <- true
			}
		}()
	} else {
		fmt.Println("⚠ 切割轮询已禁用")
		cutStarted <- true // 发送成功信号，避免阻塞
	}

	// 启动识别轮询机制
	if recogEnabled {
		go func() {
			err := recogPollingManager.Start()
			if err != nil {
				log.Printf("启动识别轮询机制失败: %v", err)
				recogStarted <- false
			} else {
				fmt.Println("✓ 识别轮询启动成功")
				recogStarted <- true
			}
		}()
	} else {
		fmt.Println("⚠ 识别轮询已禁用")
		recogStarted <- true // 发送成功信号，避免阻塞
	}

	// 等待轮询启动确认
	cutOk := <-cutStarted
	recogOk := <-recogStarted

	if !cutOk || !recogOk {
		log.Fatal("轮询系统启动失败")
	}

	// 启动状态监控协程
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				fmt.Printf("\n=== 系统状态监控 ===\n")

				if cutEnabled && cutPollingManager != nil {
					cutStatus := cutPollingManager.GetStatus()
					fmt.Printf("切割轮询: 运行=%v, 处理科目数=%d\n",
						cutStatus["running"], cutStatus["subject_count"])
				} else {
					fmt.Printf("切割轮询: 已禁用\n")
				}

				if recogEnabled && recogPollingManager != nil {
					recogStatus := recogPollingManager.GetStatus()
					fmt.Printf("识别轮询: 运行=%v, 处理科目数=%d\n",
						recogStatus["running"], recogStatus["subject_count"])
				} else {
					fmt.Printf("识别轮询: 已禁用\n")
				}

				fmt.Printf("==================\n\n")
			}
		}
	}()

	// 设置信号处理，优雅退出
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	fmt.Println("轮询系统启动完成！")
	if cutEnabled && recogEnabled {
		fmt.Println("切割轮询和识别轮询正在并行运行...")
	} else if cutEnabled {
		fmt.Println("仅切割轮询正在运行...")
	} else if recogEnabled {
		fmt.Println("仅识别轮询正在运行...")
	} else {
		fmt.Println("所有轮询都已禁用，系统处于待机状态...")
	}
	fmt.Println("状态监控每30秒更新一次")
	fmt.Println("按 Ctrl+C 安全退出系统")

	// 等待退出信号
	<-sigChan
	fmt.Println("\n收到退出信号，正在安全关闭系统...")

	// 并行停止启用的轮询管理器
	var stopWg sync.WaitGroup

	if cutEnabled && cutPollingManager != nil {
		stopWg.Add(1)
		go func() {
			defer stopWg.Done()
			fmt.Println("正在停止切割轮询...")
			cutPollingManager.Stop()
			fmt.Println("切割轮询已停止")
		}()
	}

	if recogEnabled && recogPollingManager != nil {
		stopWg.Add(1)
		go func() {
			defer stopWg.Done()
			fmt.Println("正在停止识别轮询...")
			recogPollingManager.Stop()
			fmt.Println("识别轮询已停止")
		}()
	}

	// 等待所有启用的轮询都停止
	stopWg.Wait()

	fmt.Println("系统已安全退出")
}
