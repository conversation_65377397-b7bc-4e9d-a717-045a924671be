package cut

import (
	"fmt"
	"imgProcServe/job"
	"sync"
	"time"
)

// PollingManager 轮询管理器
type PollingManager struct {
	prioritySignals map[int]*PrioritySignal // 科目ID -> 优先级信号
	mutex           sync.RWMutex
	stopChan        chan bool
	running         bool
}

// NewPollingManager 创建新的轮询管理器
func NewPollingManager() *PollingManager {
	return &PollingManager{
		prioritySignals: make(map[int]*PrioritySignal),
		stopChan:        make(chan bool, 1),
		running:         false,
	}
}

// Start 启动轮询机制
func (pm *PollingManager) Start() error {
	if pm.running {
		return fmt.Errorf("轮询管理器已经在运行")
	}

	pm.running = true
	fmt.Println("启动考试切割轮询机制...")

	// 启动优先级监控协程
	go pm.priorityMonitor()

	// 主轮询逻辑
	go pm.mainPolling()

	return nil
}

// Stop 停止轮询机制
func (pm *PollingManager) Stop() {
	if !pm.running {
		return
	}

	fmt.Println("停止考试切割轮询机制...")
	pm.running = false
	pm.stopChan <- true

	// 停止所有正在处理的科目
	pm.mutex.Lock()
	for subjectId, signal := range pm.prioritySignals {
		fmt.Printf("停止科目 %d 的处理\n", subjectId)
		select {
		case signal.Stop <- true:
		default:
		}
	}
	pm.mutex.Unlock()
}

// priorityMonitor 优先级监控协程 - 每2分钟检查一次高优先级考试
func (pm *PollingManager) priorityMonitor() {
	ticker := time.NewTicker(2 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-pm.stopChan:
			fmt.Println("优先级监控协程退出")
			return
		case <-ticker.C:
			if !pm.running {
				return
			}
			pm.checkPriorityExams()
		}
	}
}

// checkPriorityExams 检查高优先级考试
func (pm *PollingManager) checkPriorityExams() {
	fmt.Println("检查高优先级考试...")

	// 获取高优先级考试列表
	priorityExams, err := job.GetCutPriorityExamList()
	if err != nil {
		fmt.Printf("获取高优先级考试列表失败: %v\n", err)
		return
	}

	if len(priorityExams) > 0 {
		fmt.Printf("发现 %d 个切割任务高优先级考试\n", len(priorityExams))

		// 通知所有正在处理的科目有高优先级考试
		pm.mutex.RLock()
		for subjectId, signal := range pm.prioritySignals {
			select {
			case signal.HasPriority <- true:
				fmt.Printf("通知科目 %d 有高优先级考试\n", subjectId)
			default:
				// 通道已满，跳过
			}
		}
		pm.mutex.RUnlock()

		// 处理高优先级考试
		pm.processExams(priorityExams, true)
	}
}

// mainPolling 主轮询逻辑
func (pm *PollingManager) mainPolling() {
	for pm.running {
		select {
		case <-pm.stopChan:
			fmt.Println("主轮询协程退出")
			return
		default:
		}

		// 1. 优先处理高优先级考试
		priorityExams, err := job.GetCutPriorityExamList()
		if err != nil {
			fmt.Printf("获取切割任务高优先级考试列表失败: %v\n", err)
		} else if len(priorityExams) > 0 {
			fmt.Printf("切割任务：处理 %d 个高优先级考试\n", len(priorityExams))
			pm.processExams(priorityExams, true)
			continue // 处理完高优先级考试后重新开始循环
		}

		// 2. 处理普通考试
		normalExams, err := job.GetExamList()
		if err != nil {
			fmt.Printf("获取切割普通考试列表失败: %v\n", err)
			time.Sleep(10 * time.Second)
			continue
		}

		if len(normalExams) == 0 {
			fmt.Println("没有待处理切割任务的考试，等待10秒后重新检查...")
			time.Sleep(10 * time.Second)
			continue
		}

		fmt.Printf("切割任务：处理 %d 个普通考试\n", len(normalExams))
		pm.processExams(normalExams, false)

		// 处理完一轮后稍作休息，避免过度占用CPU
		time.Sleep(3 * time.Second)
	}
}

// processExams 处理考试列表
func (pm *PollingManager) processExams(exams []job.Exam, isPriority bool) {
	examType := "普通"
	if isPriority {
		examType = "高优先级"
	}

	for _, exam := range exams {
		if !pm.running {
			return
		}

		fmt.Printf("开始处理切割任务%s考试: %s (ID: %d)\n", examType, exam.ExamName, exam.ExamId)

		// 获取考试的科目列表
		subjects, err := getCutSubjectList(exam.ExamId)
		if err != nil {
			fmt.Printf("获取切割任务考试 %d 的科目列表失败: %v\n", exam.ExamId, err)
			continue
		}

		if len(subjects) == 0 {
			fmt.Printf("考试 %d 没有可处理切割任务的科目\n", exam.ExamId)
			continue
		}

		fmt.Printf("考试 %d 有 %d 个可处理切割任务的科目\n", exam.ExamId, len(subjects))

		// 处理每个科目
		for _, subject := range subjects {
			if !pm.running {
				return
			}

			pm.processSubject(subject.SubjectId, isPriority)
		}
	}
}

// processSubject 处理单个科目
func (pm *PollingManager) processSubject(subjectId int, isPriority bool) {
	fmt.Printf("开始处理科目 %d 的切割任务\n", subjectId)

	// 创建优先级信号
	prioritySignal := &PrioritySignal{
		HasPriority: make(chan bool, 1),
		Stop:        make(chan bool, 1),
	}

	// 注册到管理器
	pm.mutex.Lock()
	pm.prioritySignals[subjectId] = prioritySignal
	pm.mutex.Unlock()

	// 处理完成后清理
	defer func() {
		pm.mutex.Lock()
		delete(pm.prioritySignals, subjectId)
		pm.mutex.Unlock()
		fmt.Printf("科目 %d 切割任务处理完成\n", subjectId)
	}()

	// 调用Cut函数处理科目
	err := Cut(subjectId, prioritySignal)
	if err != nil {
		fmt.Printf("科目 %d 切割任务处理失败: %v\n", subjectId, err)
	}
}

// GetStatus 获取轮询管理器状态
func (pm *PollingManager) GetStatus() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	processingSubjects := make([]int, 0, len(pm.prioritySignals))
	for subjectId := range pm.prioritySignals {
		processingSubjects = append(processingSubjects, subjectId)
	}

	return map[string]interface{}{
		"running":             pm.running,
		"processing_subjects": processingSubjects,
		"subject_count":       len(pm.prioritySignals),
	}
}
