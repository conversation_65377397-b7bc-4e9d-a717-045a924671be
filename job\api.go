package job

import (
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"imgProcServe/middleware"
	"log"
	"reflect"
	"strconv"
	"strings"
)

type HttpMethod string

const (
	GET      HttpMethod = "GET"
	POST     HttpMethod = "POST"
	PostBody HttpMethod = "POSTBodyList"
)

// FetchData 通用HTTP请求方法，可处理任意返回类型
// 返回值：1 数据data或row；2 error
func FetchData[T any, R any](j *Client, method HttpMethod, endpoint string, params map[string]string, body interface{}) (R, error) {
	var result R
	var client = j.HttpClient // 执行HTTP请求
	// 检查客户端是否可用
	if client == nil {
		middleware.LogError(fmt.Errorf("HTTP client is not initialized"), 2)
		return result, fmt.Errorf("HTTP client is not initialized:%s", endpoint)
	}
ReStart:
	// 执行HTTP请求
	var response *resty.Response
	var err error
	switch method {
	case GET:
		response, err = client.Get(endpoint, params)
	case POST:
		response, err = client.PostBody(endpoint, params)
	case PostBody:
		response, err = client.PostBodyList(endpoint, body)
	default:
		middleware.LogError(fmt.Errorf("unsupported HTTP method: %s", method), 2)
		return result, fmt.Errorf("%s unsupported HTTP method: %s", endpoint, method)
	}

	if err != nil {
		j.HttpClient.Token = ""

		middleware.LogError(fmt.Errorf("fetch data error: %s", err.Error()), 2)
		return result, fmt.Errorf("%s fetch data error: %w", endpoint, err)
	}
	client.Response = response
	// 检查HTTP状态码
	if response.StatusCode() < 200 || response.StatusCode() >= 300 {
		middleware.LogError(fmt.Errorf("HTTP request failed with status code: %d", response.StatusCode()), 2)
		return result, fmt.Errorf("%s HTTP request failed with status code: %d", endpoint, response.StatusCode())
	}

	// 检查响应和响应体
	if response == nil {
		middleware.LogError(fmt.Errorf("empty response received"), 2)
		return result, fmt.Errorf("%s empty response received", endpoint)
	}

	responseBody := response.Body()
	if responseBody == nil || len(responseBody) == 0 {
		middleware.LogError(fmt.Errorf("empty response body"), 2)
		return result, fmt.Errorf("%s empty response body", endpoint)
	}

	// 定义通用结果结构
	var apiResult struct {
		Total int         `json:"total"`
		Code  interface{} `json:"code"`
		Msg   string      `json:"msg"`
		Data  T           `json:"data,omitempty"`
		Rows  T           `json:"rows,omitempty"`
	}

	// 解析结果
	err = json.Unmarshal(response.Body(), &apiResult)
	if err != nil {
		middleware.LogError(fmt.Errorf("unmarshal result error: %s", err.Error()), 2)
		return result, fmt.Errorf("%s unmarshal result error: %w", endpoint, err)
	}

	var code int
	switch v := apiResult.Code.(type) {
	case int:
		code = v
	case float64:
		code = int(v)
	case string:
		code, err = strconv.Atoi(v)
		if err != nil {
			middleware.LogError(fmt.Errorf("转换code失败 %s", err.Error()), 2)
			return result, fmt.Errorf("%s 转换code失败 %s", endpoint, err.Error())
		}
	default:
		code = -1
	}

	client.Msg = apiResult.Msg
	client.Total = apiResult.Total
	client.Code = code

	for {
		if code == 401 {
			err := Login()
			log.Print("用户登陆超时，重新获取用户...")
			if err == nil {
				log.Print("重新获取用户成功...")
				client.SetToken(LoginUser.UserInfo.Token)
				goto ReStart
			}
		} else {
			break
		}
	}

	// 检查API返回状态
	if code == 200 {
		// 判断数据位置（在Data字段或Rows字段）
		var data T
		if !reflect.ValueOf(apiResult.Data).IsZero() {
			data = apiResult.Data
			apiResult.Total = -1
		} else if !reflect.ValueOf(apiResult.Rows).IsZero() {
			data = apiResult.Rows
		}

		// 将获取的数据转换为所需的返回类型
		// 检查data是否为空值
		if reflect.ValueOf(data).IsZero() {
			// 如果API成功但没有数据，可能是个别接口的正常情况
			//middleware.LogError(fmt.Errorf("API returned success but no data"), 2)
			return result, nil //空数据不再往下解析
		}

		// 将获取的数据转换为所需的返回类型
		dataBytes, err := json.Marshal(data)
		if err != nil {
			middleware.LogError(fmt.Errorf("marshal data error: %s", err.Error()), 2)
			return result, fmt.Errorf("%s marshal data error: %s", endpoint, err.Error())
		}

		if len(dataBytes) == 0 || string(dataBytes) == "null" {
			middleware.LogError(fmt.Errorf("empty or null data returned after marshaling"), 2)
			return result, fmt.Errorf("%s empty or null data returned after marshaling", endpoint)
		}

		err = json.Unmarshal(dataBytes, &result)
		if err != nil {
			middleware.LogError(fmt.Errorf("unmarshal to result type error: %s", err.Error()), 2)
			return result, fmt.Errorf("%s unmarshal to result type error: %s", endpoint, err.Error())
		}

		return result, nil
	} else {
		middleware.LogError(fmt.Errorf("api error code: %d, msg: %s", code, apiResult.Msg), 2)
		return result, fmt.Errorf("%s api error code: %d, msg: %s", endpoint, code, apiResult.Msg)
	}
}

// FetchFileData 增强版本：专门处理文件下载的方法
func FetchFileData(j *Client, method HttpMethod, endpoint string, params map[string]string, body interface{}) ([]byte, error) {
	var client = j.HttpClient

	if client == nil {
		return nil, fmt.Errorf("HTTP client is not initialized:%s", endpoint)
	}

ReStart:
	var response *resty.Response
	var err error

	switch method {
	case GET:
		response, err = client.Get(endpoint, params)
	case POST:
		response, err = client.PostBody(endpoint, params)
	case PostBody:
		response, err = client.PostBodyList(endpoint, body)
	default:
		return nil, fmt.Errorf("%s unsupported HTTP method: %s", endpoint, method)
	}

	if err != nil {
		j.HttpClient.Token = ""
		return nil, fmt.Errorf("%s fetch data error: %w", endpoint, err)
	}

	if response.StatusCode() < 200 || response.StatusCode() >= 300 {
		return nil, fmt.Errorf("%s HTTP request failed with status code: %d", endpoint, response.StatusCode())
	}

	responseBody := response.Body()
	if responseBody == nil || len(responseBody) == 0 {
		return nil, fmt.Errorf("%s empty response body", endpoint)
	}

	// 检查是否需要重新登录
	_ = response.Header().Get("Content-Type")
	if strings.Contains(strings.ToLower(string(responseBody)), "unauthorized") ||
		response.StatusCode() == 401 {
		err := Login()
		if err == nil {
			client.SetToken(LoginUser.UserInfo.Token)
			goto ReStart
		}
		return nil, fmt.Errorf("authentication failed")
	}

	return responseBody, nil
}

/*
// 使用示例 - 获取科目列表 (GET请求)
func GetSubjectList(examId int) ([]Subject, bool) {
    params := make(map[string]string)
    params["examId"] = strconv.Itoa(examId)
    params["filterHasTask"] = "1"
    params["pageNumEx"] = "1"
    params["pageSizeEx"] = "100000"

    return FetchData[[]Subject, []Subject](GET, "/scan/examSubject/getSubjectListForCut", params, nil)
}

// 另一个示例 - 获取单个科目信息 (GET请求)
func GetSubject(subjectId int) (*Subject, bool) {
    params := make(map[string]string)
    params["subjectId"] = strconv.Itoa(subjectId)

    return FetchData[Subject, *Subject](GET, "/scan/examSubject/getSubject", params, nil)
}

// 示例 - 获取考试信息 (GET请求)
func GetExamInfo(examId int) (*ExamInfo, bool) {
    params := make(map[string]string)
    params["examId"] = strconv.Itoa(examId)

    return FetchData[ExamInfo, *ExamInfo](GET, "/scan/exam/getExam", params, nil)
}

// 示例 - 提交某个测试数据 (POST请求)
func SubmitTestData(testData TestDataRequest) (*TestDataResponse, bool) {
    // POST请求通常无需查询参数，但也可以添加
    params := make(map[string]string)

    return FetchData[TestDataResponse, *TestDataResponse](POST, "/scan/test/submit", params, testData)
}
*/
