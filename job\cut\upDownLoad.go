package cut

import (
	"context"
	"fmt"
	"imgProcServe/job"
	"imgProcServe/middleware"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// createFileService 创建文件服务实例
func createFileService(imgUpDownLoadHeader string) (middleware.FileService, error) {
	// 获取MinIO配置
	minioConfig := job.GetMinIOConfig()

	// 根据文件服务类型创建相应的服务
	return middleware.CreateFileServiceFromSubjectConfig(
		imgUpDownLoadHeader,
		job.FileStoreType,
		minioConfig,
	)
}

func (ct *Task) DownLoad() {
	fileService, err := createFileService(ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader)
	if err != nil {
		ct.TaskError = fmt.Errorf("创建文件服务失败：%v", err)
		return
	}

	for _, downImg := range ct.ImageDownLoad {
		// 确保目标目录存在
		if err := ensureDirectoryExists(downImg.ImageDownLoadPath); err != nil {
			ct.TaskError = fmt.Errorf("创建目录失败：%v", err)
			return
		}

		// 执行下载
		err := fileService.DownloadFile(downImg.PreHttpPath+downImg.ImageHttpPath, downImg.ImageDownLoadPath)
		if err != nil {
			ct.TaskError = fmt.Errorf("原图下载失败：%v", err)
			return
		}

		// 下载完成后立即验证文件
		if err := verifyDownloadedFile(downImg.ImageDownLoadPath); err != nil {
			ct.TaskError = fmt.Errorf("下载文件验证失败：%v", err)
			return
		}
	}
}

func (ct *Task) UpLoad() {
	// 对于上传，Apache需要添加"/file_uploadfile"路径，MinIO不需要
	uploadHeader := ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
	if job.FileStoreType != "minio" {
		uploadHeader += "/file_uploadfile"
	}

	fileService, err := createFileService(uploadHeader)
	if err != nil {
		ct.TaskError = fmt.Errorf("创建文件服务失败：%v", err)
		return
	}

	for _, upImg := range ct.ImageUpLoad {
		remotePath := "C:" + upImg.ImageHttpPath
		dir := filepath.Dir(remotePath)
		volume := filepath.VolumeName(dir)
		remotePath = strings.TrimPrefix(dir, volume)
		err := fileService.UploadFile(upImg.UpLoadImageSourcePath, remotePath)
		if err != nil {
			ct.TaskError = fmt.Errorf("切割图像上传失败：%v", err)
			return
		}
	}
}

// ensureDirectoryExists 确保目录存在
func ensureDirectoryExists(filePath string) error {
	dir := filepath.Dir(filePath)
	return os.MkdirAll(dir, 0755)
}

// verifyDownloadedFile 验证单个下载文件
func verifyDownloadedFile(filePath string) error {
	// 等待文件系统同步
	time.Sleep(time.Millisecond * 50)

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("下载的文件不存在: %s", filePath)
		}
		return fmt.Errorf("无法获取文件信息: %s, 错误: %w", filePath, err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return fmt.Errorf("下载的文件为空: %s", filePath)
	}

	// 尝试打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("无法打开下载的文件: %s, 错误: %w", filePath, err)
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	// 尝试读取文件开头确保文件完整
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && n == 0 {
		return fmt.Errorf("下载的文件无法读取: %s, 错误: %w", filePath, err)
	}

	return nil
}

// DownLoadWithRetry 带重试和超时机制的下载方法
func (ct *Task) DownLoadWithRetry() {
	const maxRetries = 3
	const downloadTimeout = 10 * time.Second

	for _, downImg := range ct.ImageDownLoad {
		// 确保目标目录存在
		if err := ensureDirectoryExists(downImg.ImageDownLoadPath); err != nil {
			ct.TaskError = fmt.Errorf("创建目录失败：%v", err)
			return
		}

		// 重试下载
		var lastErr error
		for retry := 0; retry < maxRetries; retry++ {
			// 创建带超时的上下文
			ctx, cancel := context.WithTimeout(context.Background(), downloadTimeout)

			// 执行下载
			err := ct.downloadFileWithTimeout(ctx, downImg)
			cancel()

			if err == nil {
				// 下载成功，验证文件
				if verifyErr := verifyDownloadedFile(downImg.ImageDownLoadPath); verifyErr == nil {
					break // 下载并验证成功，跳出重试循环
				} else {
					lastErr = fmt.Errorf("下载文件验证失败：%v", verifyErr)
				}
			} else {
				lastErr = err
			}

			// 如果不是最后一次重试，等待后重试
			if retry < maxRetries-1 {
				waitTime := time.Duration(retry+1) * time.Second
				fmt.Printf("下载失败，%v后重试 (第%d次): %v\n", waitTime, retry+1, lastErr)
				time.Sleep(waitTime)
			}
		}

		// 如果所有重试都失败了
		if lastErr != nil {
			ct.TaskError = fmt.Errorf("原图下载失败(重试%d次后)：%v", maxRetries, lastErr)
			return
		}
	}
}

// downloadFileWithTimeout 带超时的文件下载
func (ct *Task) downloadFileWithTimeout(ctx context.Context, downImg ImageDownLoad) error {
	fileService, err := createFileService(ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader)
	if err != nil {
		return fmt.Errorf("创建文件服务失败：%v", err)
	}

	// 创建一个通道来接收下载结果
	resultChan := make(chan error, 1)

	go func() {
		err := fileService.DownloadFile(downImg.PreHttpPath+downImg.ImageHttpPath, downImg.ImageDownLoadPath)
		resultChan <- err
	}()

	select {
	case err := <-resultChan:
		return err
	case <-ctx.Done():
		return fmt.Errorf("下载超时: %v", ctx.Err())
	}
}

// UpLoadWithRetry 带重试和超时机制的上传方法
func (ct *Task) UpLoadWithRetry() {
	const maxRetries = 3
	const uploadTimeout = 30 * time.Second

	for _, upImg := range ct.ImageUpLoad {
		// 重试上传
		var lastErr error
		for retry := 0; retry < maxRetries; retry++ {
			// 创建带超时的上下文
			ctx, cancel := context.WithTimeout(context.Background(), uploadTimeout)

			// 执行上传
			err := ct.uploadFileWithTimeout(ctx, upImg)
			cancel()

			if err == nil {
				break // 上传成功，跳出重试循环
			}

			lastErr = err

			// 如果不是最后一次重试，等待后重试
			if retry < maxRetries-1 {
				waitTime := time.Duration(retry+1) * 2 * time.Second
				fmt.Printf("上传失败，%v后重试 (第%d次): %v\n", waitTime, retry+1, lastErr)
				time.Sleep(waitTime)
			}
		}

		// 如果所有重试都失败了
		if lastErr != nil {
			ct.TaskError = fmt.Errorf("切割图像上传失败(重试%d次后)：%v", maxRetries, lastErr)
			return
		}
	}
}

// uploadFileWithTimeout 带超时的文件上传
func (ct *Task) uploadFileWithTimeout(ctx context.Context, upImg ImageUpLoad) error {
	// 对于上传，Apache需要添加"/file_uploadfile"路径，MinIO不需要
	uploadHeader := ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
	if job.FileStoreType != "minio" {
		uploadHeader += "/file_uploadfile"
	}

	fileService, err := createFileService(uploadHeader)
	if err != nil {
		return fmt.Errorf("创建文件服务失败：%v", err)
	}

	// 创建一个通道来接收上传结果
	resultChan := make(chan error, 1)

	go func() {
		remotePath := "C:" + upImg.ImageHttpPath
		dir := filepath.Dir(remotePath)
		volume := filepath.VolumeName(dir)
		if job.FileStoreType == "loc" {
			remotePath = strings.TrimPrefix(dir, volume)
		} else {
			remotePath = strings.TrimPrefix(remotePath, volume+"/")
			remotePath = strings.ReplaceAll(remotePath, "/", "\\")
		}

		err := fileService.UploadFile(upImg.UpLoadImageSourcePath, remotePath)
		resultChan <- err
	}()

	select {
	case err := <-resultChan:
		return err
	case <-ctx.Done():
		return fmt.Errorf("上传超时: %v", ctx.Err())
	}
}
