package cgo

import (
	"strconv"
	"sync"
	"testing"
)

func Test_imgCut(t *testing.T) {

	var wg sync.WaitGroup
	cutcnt := 1000
	wg.Add(cutcnt)
	for i := 0; i < cutcnt; i++ {
		//procJson := CreateCutJson(i)
		//go imgCut(procJson, &wg)
	}
	wg.Wait()
}

// CreateCutJson 创建cutJson
func CreateCutJson(imageCnt int) *CutJson {
	//遮挡区域数组
	var CutSourceImagesCoverRectsArr []CutSourceImagesCoverRectsJson
	CutSourceImagesCoverRect1 := CutSourceImagesCoverRectsJson{Height: 100, Width: 100, Left: 200, Top: 200}
	CutSourceImagesCoverRect2 := CutSourceImagesCoverRectsJson{Height: 200, Width: 200, Left: 100, Top: 100}
	CutSourceImagesCoverRectsArr = append(CutSourceImagesCoverRectsArr, CutSourceImagesCoverRect1)
	CutSourceImagesCoverRectsArr = append(CutSourceImagesCoverRectsArr, CutSourceImagesCoverRect2)

	//输入图像
	var CutSourceImages []CutSourceImagesJson
	CutSourceImage1 := CutSourceImagesJson{
		SourceImagePath:  "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000001.TIF",
		ImageRotateAngle: 90,
		ModelHeight:      2480,
		ModelWidth:       1753,
		//CutSourceImagesCoverRects: CutSourceImagesCoverRectsArr,
	}
	CutSourceImage2 := CutSourceImagesJson{
		SourceImagePath:  "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000002.TIF",
		ImageRotateAngle: 270,
		ModelHeight:      2480,
		ModelWidth:       1753,
		//CutSourceImagesCoverRects: CutSourceImagesCoverRectsArr,
	}
	CutSourceImages = append(CutSourceImages, CutSourceImage1)
	CutSourceImages = append(CutSourceImages, CutSourceImage2)

	//切割区域
	var CutInImageRectsArr1 []CutInImageRectsJson
	CutInImageRect1 := CutInImageRectsJson{Height: 696, Width: 880, Left: 63, Top: 945}
	CutInImageRect2 := CutInImageRectsJson{Height: 626, Width: 833, Left: 846, Top: 345}
	CutInImageRectsArr1 = append(CutInImageRectsArr1, CutInImageRect1)
	CutInImageRectsArr1 = append(CutInImageRectsArr1, CutInImageRect2)

	var CutInImageRectsArr2 []CutInImageRectsJson
	CutInImageRect3 := CutInImageRectsJson{Height: 713, Width: 790, Left: 803, Top: 971}
	CutInImageRectsArr2 = append(CutInImageRectsArr2, CutInImageRect3)

	//切割块信息
	var CutInImagesArr []CutInImagesJson
	CutInImage1 := CutInImagesJson{
		InImagePath: "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000001.TIF",
		CutRects:    CutInImageRectsArr1,
	}
	CutInImage2 := CutInImagesJson{
		InImagePath: "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000002.TIF",
		CutRects:    CutInImageRectsArr2,
	}
	CutInImagesArr = append(CutInImagesArr, CutInImage1)
	CutInImagesArr = append(CutInImagesArr, CutInImage2)

	//输出图像
	var CutOutputImagesArr []CutOutputImagesJson
	CutOutputImage1 := CutOutputImagesJson{
		OutputImagePaths: "C:\\Users\\<USER>\\Desktop\\test_auto\\cutimg\\cut_" + strconv.Itoa(imageCnt+1) + ".jpg",
		JoinDirect:       "V",
		JoinAlignType:    1,
		CutInImages:      CutInImagesArr,
	}
	CutOutputImagesArr = append(CutOutputImagesArr, CutOutputImage1)

	cutJson := CutJson{
		Function:        "concat",
		Ratio:           10,
		CutSourceImages: CutSourceImages,
		CutOutputImages: CutOutputImagesArr,
	}
	return &cutJson
}
