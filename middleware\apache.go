package middleware

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// FileService 定义文件服务接口
type FileService interface {
	UploadFile(filePath, remotePath string) error
	DownloadFile(remotePath, localPath string) error
}

// ApacheFileClient 提供与Apache文件服务器交互的功能
type ApacheFileClient struct {
	BaseURL      string // Apache服务器的基础URL
	UserAgent    string // 用户代理
	CacheControl string // 缓存控制
}

// 确保ApacheFileClient实现了FileService接口
var _ FileService = (*ApacheFileClient)(nil)

// NewApacheFileClient 创建一个新的Apache文件服务实例
func NewApacheFileClient(baseURL string) *ApacheFileClient {
	return &ApacheFileClient{
		BaseURL:      baseURL,
		UserAgent:    "123",
		CacheControl: "no-cache",
	}
}

// UploadFile 上传文件到Apache服务器
// filePath: 本地文件路径
// remotePath: 远程存储路径
func (s *ApacheFileClient) UploadFile(filePath, remotePath string) error {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("无法打开文件: %w", err)
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	// 计算文件MD5
	md5Hash, err := calculateFileMD5(filePath)
	if err != nil {
		return fmt.Errorf("计算MD5失败: %w", err)
	}

	// 创建multipart请求
	var requestBody bytes.Buffer
	multipartWriter := multipart.NewWriter(&requestBody)

	// 添加dir参数
	err = multipartWriter.WriteField("dir", remotePath)
	if err != nil {
		return fmt.Errorf("写入dir字段失败: %w", err)
	}

	// 添加md5参数
	err = multipartWriter.WriteField("md5", md5Hash)
	if err != nil {
		return fmt.Errorf("写入md5字段失败: %w", err)
	}

	// 添加文件
	fileWriter, err := multipartWriter.CreateFormFile("fileName", filepath.Base(filePath))
	if err != nil {
		return fmt.Errorf("创建form文件字段失败: %w", err)
	}

	_, err = io.Copy(fileWriter, file)
	if err != nil {
		return fmt.Errorf("复制文件内容失败: %w", err)
	}

	// 完成multipart写入
	err = multipartWriter.Close()
	if err != nil {
		return fmt.Errorf("关闭multipart writer失败: %w", err)
	}

	// 创建HTTP请求
	uploadURL := fmt.Sprintf("%s/upload", s.BaseURL)
	req, err := http.NewRequest("POST", uploadURL, &requestBody)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置Content-Type
	req.Header.Set("Content-Type", multipartWriter.FormDataContentType())
	req.Header.Set("User-Agent", s.UserAgent)
	req.Header.Set("Cache-Control", s.CacheControl)

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		//respBody, _ := io.ReadAll(resp.Body)
		//return fmt.Errorf("上传失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		return fmt.Errorf("上传失败，状态码: %d", resp.StatusCode)
	}

	// 读取并返回响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查响应中是否包含错误信息
	responseStr := string(respBody)
	if strings.Contains(strings.ToLower(responseStr), "error") || strings.Contains(strings.ToLower(responseStr), "failed") {
		return fmt.Errorf("服务器返回错误: %s", responseStr)
	}

	return nil
}

// DownloadFile 从Apache服务器下载文件
// remotePath: 远程文件路径
// localPath: 本地保存路径
func (s *ApacheFileClient) DownloadFile(remotePath, localPath string) error {
	// 构建下载URL
	//downloadURL := fmt.Sprintf("%s/download?path=%s", s.BaseURL, remotePath)

	// 创建HTTP请求
	resp, err := http.Get(remotePath)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer func(Body io.ReadCloser) {
		_ = Body.Close()
	}(resp.Body)

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		//respBody, _ := io.ReadAll(resp.Body)
		//return fmt.Errorf("下载失败，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 确保本地目录存在
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0755); err != nil {
		return fmt.Errorf("创建本地目录失败: %w", err)
	}

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer func(localFile *os.File) {
		_ = localFile.Close()
	}(localFile)

	// 将响应内容写入本地文件
	_, err = io.Copy(localFile, resp.Body)
	if err != nil {
		return fmt.Errorf("写入文件内容失败: %w", err)
	}

	return nil
}

// 计算文件的MD5值
func calculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}
