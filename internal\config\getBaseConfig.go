package config

import (
	"fmt"
	"gopkg.in/ini.v1"
	"os"
	"strconv"
)

var BaseConfig Config

type Config struct {
	IniConfig  IniConfig
	MacAddress []string
	ExecDir    string
	ServerUrl  string
}

// IniConfig 应用服务地址信息
type IniConfig struct {
	AppHost         AppHost `ini:"SetIP"`
	TempDir         string  `ini:"TempDir"`
	CutImageTempDir string  //切割临时目录
	CutLogDir       string  //切割日志
	RecogTempDir    string  //识别临时目录
	RecogLogDir     string  //识别日志
}

type AppHost struct {
	Type string `ini:"UrlType"`
	Host string `ini:"MainIP"`
	Port int    `ini:"Port"`
}

// GetConfig 获取配置文件信息、mac地址
func GetConfig() error {
	//获取HytCrypto.ini路径
	configPath, err := GetConfigPath()
	if err != nil {
		return err
	}
	//检查HytCrypto.ini是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		err := fmt.Errorf("配置文件不存在: %s", configPath)
		os.Exit(1)
		return err
	} else if err != nil {
		err := fmt.Errorf("配置文件读取失败: %v", err)
		os.Exit(1)
		return err
	}

	file, err := ini.Load(configPath)
	if err != nil {
		return fmt.Errorf("配置文件读取错误，请检查文件路径：%v", err)
	}
	//配置信息
	err = file.MapTo(&BaseConfig.IniConfig)
	SetDefaults()
	if err != nil {
		return fmt.Errorf("配置文件映射配置信息失败: %v", err)
	}

	BaseConfig.MacAddress, err = GetMacAddresses(false)
	if err != nil {
		return fmt.Errorf("获取MAC地址失败: %v", err)
	}

	return nil
}

// SetDefaults 配置文件设置默认值
func SetDefaults() {
	if BaseConfig.IniConfig.AppHost.Port == 0 {
		BaseConfig.IniConfig.AppHost.Port = 8881
	}
	if BaseConfig.IniConfig.AppHost.Type == "" {
		BaseConfig.IniConfig.AppHost.Type = "http"
	}
	if BaseConfig.IniConfig.TempDir == "" {
		BaseConfig.IniConfig.TempDir = "temp"
	}
	BaseConfig.IniConfig.CutImageTempDir = BaseConfig.ExecDir + "\\" + BaseConfig.IniConfig.TempDir + "\\cutimgtemp"
	BaseConfig.IniConfig.CutLogDir = BaseConfig.ExecDir + "\\cutlog"
	BaseConfig.IniConfig.RecogTempDir = BaseConfig.ExecDir + "\\" + BaseConfig.IniConfig.TempDir + "\\recogtemp"
	BaseConfig.IniConfig.RecogLogDir = BaseConfig.ExecDir + "\\recoglog"
}

func LoadConfig() error {
	err := GetConfig()
	if err != nil {
		return err
	}

	urlType := BaseConfig.IniConfig.AppHost.Type
	url := BaseConfig.IniConfig.AppHost.Host
	port := strconv.Itoa(BaseConfig.IniConfig.AppHost.Port)
	BaseConfig.ServerUrl = urlType + "://" + url + ":" + port
	return nil
}
