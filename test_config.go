package main

import (
	"fmt"
	"imgProcServe/internal/config"
	"log"
)

func main() {
	fmt.Println("=== 测试轮询配置功能 ===")
	
	// 加载配置
	err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	
	// 显示配置信息
	fmt.Printf("切割轮询开关: %v\n", config.BaseConfig.IniConfig.CutPollingEnabled)
	fmt.Printf("识别轮询开关: %v\n", config.BaseConfig.IniConfig.RecogPollingEnabled)
	
	// 模拟main.go中的逻辑
	cutEnabled := config.BaseConfig.IniConfig.CutPollingEnabled
	recogEnabled := config.BaseConfig.IniConfig.RecogPollingEnabled
	
	fmt.Printf("\n=== 轮询启动逻辑测试 ===\n")
	fmt.Printf("轮询配置: 切割轮询=%v, 识别轮询=%v\n", cutEnabled, recogEnabled)
	
	if cutEnabled && recogEnabled {
		fmt.Println("✓ 将启动切割轮询和识别轮询")
	} else if cutEnabled {
		fmt.Println("✓ 仅启动切割轮询")
	} else if recogEnabled {
		fmt.Println("✓ 仅启动识别轮询")
	} else {
		fmt.Println("⚠ 所有轮询都已禁用")
	}
	
	fmt.Println("\n=== 配置测试完成 ===")
}
