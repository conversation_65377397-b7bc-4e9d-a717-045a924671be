package cut

import (
	"fmt"
	"imgProcServe/internal/config"
	"imgProcServe/job"
	"imgProcServe/middleware"
	"strconv"
	"strings"
)

type SubjectConfig struct {
	ScanImgMac              string //扫描图像文件服务器Mac地址
	ScanImgFTPIP            string //扫描图像文件服务器IP地址
	ScanImgHttpContext      string //扫描图像上传下载上下文
	ScanImgLocalOrSharePath string //扫描图像文件服务器扫描图像本地或共享路径
	PjImgMac                string //评卷图像文件服务器Mac地址
	PjImgFTPIP              string //评卷图像文件服务器IP地址
	PjImgHttpContext        string //评卷图像上传下载上下文
	PjImgApacheContext      string //评卷图像浏览上下文
	FileUpDownLoadPort      int    //文件上传下载端口
	ImgBrowsePort           int    //图像浏览端口
	PjImgLocalOrSharePath   string //评卷图像文件服务器评卷图像本地或共享路径
	IndefiniteCardPjCut     bool   //题卡数量不定评卷图像切割(0关闭，1打开) ,默认0
	ItemCutModifyFlag       string //切割参数修改标识，切割端会根据这个标识重新加载切割参数
	CutRatioTh              int    //切割自适应阈值
	IfScanFileServer        bool   //是否在扫描图像文件服务器上
	IfPjFileServer          bool   //是否在评卷图像文件服务器上
	ImgUpDownLoadHeader     string //图像上传下载http head
	ImgBrowseHttpHeader     string //图像浏览http head
}

// 获取科目参数
func getSubjectCutConfig(subjectId int) (SubjectConfig, error) {
	configs := []job.XrConfig{
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgMac"},              //扫描图像文件服务器Mac地址
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgFTPIP"},            //扫描图像文件服务器IP地址
		{PId: subjectId, ConfigKey: "scan.img.port"},                        //扫描文件服务器端口
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgHttpContext"},      //扫描图像上传下载上下文
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgLocalOrSharePath"}, //扫描图像文件服务器扫描图像本地或共享路径
		{PId: subjectId, ConfigKey: "scan.subject.PjImgMac"},                //评卷图像文件服务器Mac地址
		{PId: subjectId, ConfigKey: "scan.subject.PjImgFTPIP"},              //评卷图像文件服务器IP地址
		{PId: subjectId, ConfigKey: "pj.img.port"},                          //评卷文件服务器端口
		{PId: subjectId, ConfigKey: "scan.subject.PjImgHttpContext"},        //评卷图像上传下载上下文
		{PId: subjectId, ConfigKey: "scan.subject.PjImgApacheContext"},      //评卷图像浏览上下文
		{PId: subjectId, ConfigKey: "scan.subject.PjImgLocalOrSharePath"},   //评卷图像文件服务器评卷图像本地或共享路径
		{PId: subjectId, ConfigKey: "scan.exam.indefiniteCardPjCut"},        //题卡数量不定评卷图像切割更新Value(0关闭，1打开) ,默认0
		{PId: subjectId, ConfigKey: "item.cut.modify.flag"},                 //切割参数修改标识，切割端会根据这个标识重新加载切割参数
		{PId: subjectId, ConfigKey: "scan.subject.RatioTh"},                 //切割自适应参数
	}

	ConfigArr, err := job.FetchData[[]job.XrConfig, []job.XrConfig](
		job.NewHttpClient(),
		job.PostBody,
		"/system/config/getConfigs/byPIdKeyList",
		nil,
		configs)
	if err != nil {
		return SubjectConfig{}, err
	}

	var subjectConfig SubjectConfig
	for _, v := range ConfigArr {
		if (v.ConfigValue == "" || v.ConfigValue == "-") &&
			(v.ConfigKey != "scan.subject.ScanImgHttpContext" && v.ConfigKey != "scan.subject.PjImgHttpContext" && v.ConfigKey != "scan.subject.PjImgApacheContext") {
			middleware.LogError(fmt.Errorf("警告：科目id:%d 科目参数'%s'未设置", subjectId, v.ConfigName+"-"+v.ConfigKey), 2)
			return subjectConfig, fmt.Errorf("警告：科目id:%d 科目参数'%s'未设置", subjectId, v.ConfigName+"-"+v.ConfigKey)
		}

		switch v.ConfigKey {
		case "scan.subject.ScanImgMac":
			subjectConfig.ScanImgMac = v.ConfigValue
		case "scan.subject.ScanImgFTPIP":
			subjectConfig.ScanImgFTPIP = v.ConfigValue
		case "scan.img.port":
			subjectConfig.FileUpDownLoadPort, _ = strconv.Atoi(v.ConfigValue)
		case "scan.subject.ScanImgHttpContext":
			subjectConfig.ScanImgHttpContext = v.ConfigValue
		case "scan.subject.ScanImgLocalOrSharePath":
			subjectConfig.ScanImgLocalOrSharePath = v.ConfigValue
		case "scan.subject.PjImgMac":
			subjectConfig.PjImgMac = v.ConfigValue
		case "scan.subject.PjImgFTPIP":
			subjectConfig.PjImgFTPIP = v.ConfigValue
		case "pj.img.port":
			subjectConfig.ImgBrowsePort, _ = strconv.Atoi(v.ConfigValue)
		case "scan.subject.PjImgHttpContext":
			subjectConfig.PjImgHttpContext = v.ConfigValue
		case "scan.subject.PjImgApacheContext":
			subjectConfig.PjImgApacheContext = v.ConfigValue
		case "scan.subject.PjImgLocalOrSharePath":
			subjectConfig.PjImgLocalOrSharePath = v.ConfigValue
		case "scan.exam.indefiniteCardPjCut":
			var value, _ = strconv.Atoi(v.ConfigValue)
			subjectConfig.IndefiniteCardPjCut = value != 0
		case "item.cut.modify.flag":
			subjectConfig.ItemCutModifyFlag = v.ConfigValue
		case "scan.subject.RatioTh":
			subjectConfig.CutRatioTh, _ = strconv.Atoi(v.ConfigValue)
		}
	}
	//判断本机是否是扫描评卷图像文件服务机器
	for _, mac := range config.BaseConfig.MacAddress {
		if strings.Contains(subjectConfig.ScanImgMac, mac) {
			subjectConfig.IfScanFileServer = true
			break
		}
		subjectConfig.IfScanFileServer = false
	}

	for _, mac := range config.BaseConfig.MacAddress {
		if strings.Contains(subjectConfig.PjImgMac, mac) {
			subjectConfig.IfScanFileServer = true
			break
		}
		subjectConfig.IfPjFileServer = false
	}

	if subjectConfig.ScanImgHttpContext == "-" || subjectConfig.ScanImgHttpContext == "" {
		subjectConfig.ImgUpDownLoadHeader = subjectConfig.ScanImgFTPIP + ":" + strconv.Itoa(subjectConfig.FileUpDownLoadPort)
	} else {
		subjectConfig.ImgUpDownLoadHeader = subjectConfig.ScanImgFTPIP + "/" + subjectConfig.ScanImgHttpContext
	}

	if subjectConfig.PjImgHttpContext == "-" || subjectConfig.PjImgHttpContext == "" {
		subjectConfig.ImgBrowseHttpHeader = subjectConfig.PjImgFTPIP + ":" + strconv.Itoa(subjectConfig.ImgBrowsePort)
	} else {
		subjectConfig.ImgBrowseHttpHeader = subjectConfig.PjImgFTPIP + "/" + subjectConfig.PjImgHttpContext
	}

	return subjectConfig, nil
}
