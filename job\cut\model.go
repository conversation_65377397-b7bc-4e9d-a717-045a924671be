package cut

import (
	"fmt"
	"imgProcServe/job"
	"strconv"
)

type CardTypeWhoOpenCut struct {
	CardType string `json:"cardType"`
	Rotate   string `json:"rotate"`
}
type ModelImageInfo struct {
	OmrType   string `json:"omrType"`
	ModWidth  string `json:"modWidth"`
	ModHeight string `json:"modHeight"`
	Crossx    string `json:"crossx"`
	Crossy    string `json:"crossy"`
	Angle     string `json:"angle"`
}

func getCardWhoOpenCut(subjectid int, papertype string) ([]CardTypeWhoOpenCut, error) {
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectid)
	params["cardType"] = papertype
	return job.FetchData[[]CardTypeWhoOpenCut, []CardTypeWhoOpenCut](
		job.NewHttpClient(),
		job.GET,
		"/scan/ItemCut/getCardTypeRotate",
		params,
		nil)
}

func getModelImageInfo(modelid int, cardtype string) ([]ModelImageInfo, error) {
	params := make(map[string]string)
	params["omrType"] = cardtype
	params["modelId"] = strconv.Itoa(modelid)
	return job.FetchData[[]ModelImageInfo, []ModelImageInfo](
		job.NewHttpClient(),
		job.GET,
		"/scan/modelPaper/getModelOmrNumTLHW",
		params,
		nil)
}

func getModelInfo(subjectid int, modelid int, papertype string) ([]ModelImageInfo, error) {
	var modelInfoList []ModelImageInfo
	cardWhoOpenCutList, err := getCardWhoOpenCut(subjectid, papertype)
	if err != nil {
		return nil, err
	}

	for i := 0; i < len(cardWhoOpenCutList); i++ {
		modelImageInfoList, err := getModelImageInfo(modelid, cardWhoOpenCutList[i].CardType)
		if err != nil {
			return nil, err
		} else {
			imageRotate, err := convertAngle(cardWhoOpenCutList[i].Rotate, job.OSSCutImageFlag)
			if err != nil {
				return nil, err
			}
			for j := 0; j < len(modelImageInfoList); j++ {
				modelInfoList = append(modelInfoList, ModelImageInfo{
					OmrType:   modelImageInfoList[j].OmrType,
					ModWidth:  modelImageInfoList[j].ModWidth,
					ModHeight: modelImageInfoList[j].ModHeight,
					Crossx:    modelImageInfoList[j].Crossx,
					Crossy:    modelImageInfoList[j].Crossy,
					Angle:     imageRotate})
			}
		}
	}

	return modelInfoList, nil
}

// 获取科目模版最大卡数
func getMaxCardModeCnt(subjectid int) (int, error) {
	type paperid struct {
		PaperCardId int `json:"paperCardId"`
	}
	paper, err := job.FetchData[paperid, *paperid](
		job.NewHttpClient(),
		job.GET,
		"/scan/paperCard/getPaperCard/"+strconv.Itoa(subjectid),
		nil,
		nil)

	if err != nil {
		return 0, fmt.Errorf("获取科目%d题卡id失败", subjectid)
	}

	type maxmodelid struct {
		ModelId int `json:"modelId"`
	}

	params := make(map[string]string)
	params["paperCardId"] = strconv.Itoa(paper.PaperCardId)
	maxmodel, err := job.FetchData[maxmodelid, *maxmodelid](
		job.NewHttpClient(),
		job.GET,
		"/scan/model/getMaxCardModel",
		params,
		nil)

	if err != nil {
		return 0, fmt.Errorf("获取科目%d最大题卡模版id失败", subjectid)
	}

	type cardInfo struct {
		OmrType string `json:"omrType"`
	}

	params = make(map[string]string)
	params["modelId"] = strconv.Itoa(maxmodel.ModelId)
	card, err := job.FetchData[[]cardInfo, []cardInfo](
		job.NewHttpClient(),
		job.GET,
		"/scan/modelPaper/list",
		params,
		nil)

	if err != nil {
		return 0, fmt.Errorf("获取科目%d最大模版卡信息失败", subjectid)
	}

	maxCardCnt := 0
	for i := 0; i < len(card); i++ {
		cardOmrType := card[i].OmrType
		cardCnt := cardOmrType[1:2]
		if cnt, _ := strconv.Atoi(cardCnt); cnt > maxCardCnt {
			maxCardCnt = cnt
		}
	}
	return maxCardCnt, nil
}
