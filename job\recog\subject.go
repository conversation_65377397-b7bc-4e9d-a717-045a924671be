package recog

import (
	"fmt"
	"imgProcServe/internal/config"
	"imgProcServe/job"
	"imgProcServe/middleware"
	"strconv"
	"strings"
)

type SubjectInfo struct {
	ExamId          int    `json:"examId"`
	SubjectId       int    `json:"subjectId"`
	SubjectName     string `json:"subjectName"`
	ModelId         int    `json:"modelId"` //默认模板ID
	ModelFileName   string `json:"modelFileName"`
	SubRecogTempDir string
	SubjectConfig   SubjectConfig //科目参数
}

type SubjectConfig struct {
	ScanImgMac                 string //扫描图像文件服务器Mac地址
	ScanImgFTPIP               string //扫描图像文件服务器IP地址
	ScanImgHttpContext         string //扫描图像上传下载上下文
	ScanImgLocalOrSharePath    string //扫描图像文件服务器扫描图像本地或共享路径
	FileUpDownLoadPort         int    //文件上传下载端口
	ImgUpDownLoadHeader        string //图像上传下载http head
	RecognitionAlgorithmSelect string //识别算法选择
	IfScanFileServer           bool   //是否在扫描图像文件服务器上
	IfUpdateAbsentFlag         string
}

// 按考试获取可识别科目列表
func getRecogSubjectList(examId int) ([]*SubjectInfo, error) {
	params := make(map[string]string)
	params["examId"] = strconv.Itoa(examId)
	params["filterHasTask"] = "true"
	params["pageNumEx"] = "1"
	params["pageSizeEx"] = "100000"
	subjectInfoList, err := job.FetchData[[]*SubjectInfo, []*SubjectInfo](
		job.NewHttpClient(),
		job.POST,
		"/scan/examSubject/list",
		params,
		nil)

	if err != nil {
		return nil, err
	}

	for index, subjectInfo := range subjectInfoList {
		if subjectInfo.ModelFileName == "" {
			continue
		}
		subjectInfoList[index].SubjectConfig, err = getSubjectRecogConfig(subjectInfo.SubjectId)
		subjectInfoList[index].SubRecogTempDir = config.BaseConfig.IniConfig.RecogTempDir + "\\" + strconv.Itoa(subjectInfo.SubjectId)
		if err != nil {
			return nil, err
		}
	}

	return subjectInfoList, nil
}

// 按科目获取可识别科目
func getRecogSubject(subjectId int) ([]*SubjectInfo, error) {
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectId)
	params["filterHasTask"] = "true"
	params["pageNumEx"] = "1"
	params["pageSizeEx"] = "100000"
	subjectInfoList, err := job.FetchData[[]*SubjectInfo, []*SubjectInfo](
		job.NewHttpClient(),
		job.POST,
		"/scan/examSubject/list",
		params,
		nil)

	if err != nil {
		return nil, err
	}

	for index, subjectInfo := range subjectInfoList {
		if subjectInfo.ModelFileName == "" {
			continue
		}
		subjectInfoList[index].SubjectConfig, err = getSubjectRecogConfig(subjectInfo.SubjectId)
		subjectInfoList[index].SubRecogTempDir = config.BaseConfig.IniConfig.RecogTempDir + "\\" + strconv.Itoa(subjectInfo.SubjectId)
		if err != nil {
			return nil, err
		}
	}

	return subjectInfoList, nil
}

// 获取科目参数
func getSubjectRecogConfig(subjectId int) (SubjectConfig, error) {
	configs := []job.XrConfig{
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgMac"},                 //扫描图像文件服务器Mac地址
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgFTPIP"},               //扫描图像文件服务器IP地址
		{PId: subjectId, ConfigKey: "scan.img.port"},                           //扫描文件服务器端口
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgHttpContext"},         //扫描图像上传下载上下文
		{PId: subjectId, ConfigKey: "scan.subject.ScanImgLocalOrSharePath"},    //扫描图像文件服务器扫描图像本地或共享路径
		{PId: subjectId, ConfigKey: "scan.subject.RecognitionAlgorithmSelect"}, //识别算法选择
		{PId: subjectId, ConfigKey: "omr.subject.update.absent"},               //是否更新缺考标记
	}

	ConfigArr, err := job.FetchData[[]job.XrConfig, []job.XrConfig](
		job.NewHttpClient(),
		job.PostBody,
		"/system/config/getConfigs/byPIdKeyList",
		nil,
		configs)

	if err != nil {
		return SubjectConfig{}, err
	}

	var subjectConfig SubjectConfig
	for _, v := range ConfigArr {
		if (v.ConfigValue == "" || v.ConfigValue == "-") &&
			(v.ConfigKey != "scan.subject.ScanImgHttpContext" && v.ConfigKey != "scan.subject.PjImgHttpContext" && v.ConfigKey != "scan.subject.PjImgApacheContext") {
			middleware.LogError(fmt.Errorf("警告：科目id:%d 科目参数'%s'未设置", subjectId, v.ConfigName+"-"+v.ConfigKey), 2)
			return subjectConfig, fmt.Errorf("警告：科目id:%d 科目参数'%s'未设置", subjectId, v.ConfigName+"-"+v.ConfigKey)
		}

		switch v.ConfigKey {
		case "scan.subject.ScanImgMac":
			subjectConfig.ScanImgMac = v.ConfigValue
		case "scan.subject.ScanImgFTPIP":
			subjectConfig.ScanImgFTPIP = v.ConfigValue
		case "scan.img.port":
			subjectConfig.FileUpDownLoadPort, _ = strconv.Atoi(v.ConfigValue)
		case "scan.subject.ScanImgHttpContext":
			subjectConfig.ScanImgHttpContext = v.ConfigValue
		case "scan.subject.ScanImgLocalOrSharePath":
			subjectConfig.ScanImgLocalOrSharePath = v.ConfigValue
		case "scan.subject.RecognitionAlgorithmSelect":
			subjectConfig.RecognitionAlgorithmSelect = v.ConfigValue
		case "omr.subject.update.absent":
			subjectConfig.IfUpdateAbsentFlag = v.ConfigValue
		}
	}
	//判断本机是否是扫描图像文件服务机器
	for _, mac := range config.BaseConfig.MacAddress {
		if strings.Contains(subjectConfig.ScanImgMac, mac) {
			subjectConfig.IfScanFileServer = true
			break
		}
		subjectConfig.IfScanFileServer = false
	}

	if subjectConfig.ScanImgHttpContext == "-" || subjectConfig.ScanImgHttpContext == "" {
		subjectConfig.ImgUpDownLoadHeader = subjectConfig.ScanImgFTPIP + ":" + strconv.Itoa(subjectConfig.FileUpDownLoadPort)
	} else {
		subjectConfig.ImgUpDownLoadHeader = subjectConfig.ScanImgFTPIP + "/" + subjectConfig.ScanImgHttpContext
	}

	return subjectConfig, nil
}
