package job

type Exam struct {
	ExamId     int    `json:"examId"`
	ExamName   string `json:"examName"`
	CreateTime string `json:"createTime"`
}

// GetExamList 获取考试列表
func GetExamList() ([]Exam, error) {
	params := make(map[string]string)
	params["enable"] = "1"
	params["finished"] = "0"
	params["pageNum"] = "1"
	params["pageSize"] = "10000"

	return FetchData[[]Exam, []Exam](
		NewHttpClient(),
		GET,
		"/base/exam/list",
		params,
		nil)
}

func GetCutPriorityExamList() ([]Exam, error) {
	params := make(map[string]string)
	params["enable"] = "1"
	params["finished"] = "0"
	params["pageNum"] = "1"
	params["pageSize"] = "10000"
	params["cutPriority"] = "1"
	return FetchData[[]Exam, []Exam](
		NewHttpClient(),
		GET,
		"/base/exam/list",
		params,
		nil)
}
