package cgo

import (
	"fmt"
	"testing"
)

func TestModelInit(t *testing.T) {
	var model string = "C:/Users/<USER>/Desktop/test_auto/TEST_AUTO.air"
	err := ModelInit(model)
	if err != nil {
		return
	}
}

func TestRecogniseCard(t *testing.T) {
	TestModelInit(t)
	var front string = "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000001.TIF"
	var back string = "C:\\Users\\<USER>\\Desktop\\test_auto\\IA00000002.TIF"

	recogniseStr, recogError := RecogniseCard(front, back)
	if recogError == nil {
		fmt.Println("开始识别...")
		fmt.Print(recogniseStr)
	}
}
