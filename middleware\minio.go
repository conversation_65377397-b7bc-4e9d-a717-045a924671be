package middleware

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinIOFileClient 提供与MinIO文件服务器交互的功能
type MinIOFileClient struct {
	Client     *minio.Client
	BucketName string
	Endpoint   string
	AccessKey  string
	SecretKey  string
	UseSSL     bool
}

// 确保MinIOFileClient实现了FileService接口
var _ FileService = (*MinIOFileClient)(nil)

// MinIOConfig MinIO配置结构
type MinIOConfig struct {
	Endpoint   string
	AccessKey  string
	SecretKey  string
	BucketName string
	UseSSL     bool
}

// NewMinIOFileClient 创建一个新的MinIO文件服务实例
func NewMinIOFileClient(config MinIOConfig) (*MinIOFileClient, error) {
	// 初始化MinIO客户端
	minioClient, err := minio.New(config.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(config.AccessKey, config.SecretKey, ""),
		Secure: config.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("创建MinIO客户端失败: %w", err)
	}

	client := &MinIOFileClient{
		Client:     minioClient,
		BucketName: config.BucketName,
		Endpoint:   config.Endpoint,
		AccessKey:  config.AccessKey,
		SecretKey:  config.SecretKey,
		UseSSL:     config.UseSSL,
	}

	// 检查bucket是否存在，如果不存在则创建
	ctx := context.Background()
	exists, err := minioClient.BucketExists(ctx, config.BucketName)
	if err != nil {
		return nil, fmt.Errorf("检查bucket存在性失败: %w", err)
	}

	if !exists {
		err = minioClient.MakeBucket(ctx, config.BucketName, minio.MakeBucketOptions{})
		if err != nil {
			return nil, fmt.Errorf("创建bucket失败: %w", err)
		}
	}

	return client, nil
}

// UploadFile 上传文件到MinIO服务器
// filePath: 本地文件路径
// remotePath: 远程存储路径
func (m *MinIOFileClient) UploadFile(filePath, remotePath string) error {
	// 打开本地文件
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("无法打开文件: %w", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("获取文件信息失败: %w", err)
	}

	// 清理远程路径，确保使用正确的分隔符
	objectName := strings.ReplaceAll(remotePath, "\\", "/")
	objectName = strings.TrimPrefix(objectName, "/")

	// 上传文件
	ctx := context.Background()
	_, err = m.Client.PutObject(ctx, m.BucketName, objectName, file, fileInfo.Size(), minio.PutObjectOptions{
		ContentType: "application/octet-stream",
	})
	if err != nil {
		return fmt.Errorf("上传文件失败: %w", err)
	}

	return nil
}

// DownloadFile 从MinIO服务器下载文件
// remotePath: 远程文件路径
// localPath: 本地保存路径
func (m *MinIOFileClient) DownloadFile(remotePath, localPath string) error {
	// 从完整URL中提取对象名称

	objectName := m.extractObjectName(remotePath)

	if objectName == "" {
		return fmt.Errorf("无法从路径中提取对象名称: %s", remotePath)
	}

	// 确保本地目录存在
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0755); err != nil {
		return fmt.Errorf("创建本地目录失败: %w", err)
	}

	// 从MinIO下载文件
	ctx := context.Background()
	object, err := m.Client.GetObject(ctx, m.BucketName, objectName, minio.GetObjectOptions{})
	if err != nil {
		return fmt.Errorf("获取MinIO对象失败: %w", err)
	}
	defer object.Close()

	// 创建本地文件
	localFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建本地文件失败: %w", err)
	}
	defer localFile.Close()

	// 将对象内容写入本地文件
	_, err = io.Copy(localFile, object)
	if err != nil {
		return fmt.Errorf("写入文件内容失败: %w", err)
	}

	return nil
}

// extractObjectName 从完整的URL路径中提取MinIO对象名称
func (m *MinIOFileClient) extractObjectName(remotePath string) string {
	// 如果是完整的HTTP URL，提取路径部分
	if strings.HasPrefix(remotePath, "http://") || strings.HasPrefix(remotePath, "https://") {
		// 移除协议和主机部分
		parts := strings.SplitN(remotePath, "//", 2)
		if len(parts) < 2 {
			return ""
		}

		// 找到第一个斜杠后的路径
		pathParts := strings.SplitN(parts[1], "/", 2)
		if len(pathParts) < 2 {
			return ""
		}

		objectName := pathParts[1]
		// 清理路径分隔符
		objectName = strings.ReplaceAll(objectName, "\\", "/")
		return strings.TrimPrefix(objectName, "/")
	}

	// 如果不是完整URL，直接处理为对象名称
	objectName := strings.ReplaceAll(remotePath, "\\", "/")
	return strings.TrimPrefix(objectName, "/")
}
