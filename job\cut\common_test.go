package cut

import (
	"imgProcServe/job"
	"log"
	"testing"
)

func Test_getOptionRecogItem(t *testing.T) {
	err := job.Login()
	if err != nil {
		return
	}
	result, err := getOptionRecogItem("33,34;56,57", "56,57", "2/1", "01,10")
	if err != nil {
		log.Fatal(err)
	} else {
		log.Println(result)
	}
	result, err = getOptionRecogItem("33,34,35,36", "33,34,35,36", "4/2", "0101")
	if err != nil {
		log.Fatal(err)
	} else {
		log.Println(result)
	}
}
