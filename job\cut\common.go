package cut

import (
	"fmt"
	"imgProcServe/cgo"
	"imgProcServe/middleware"
	"strconv"
	"strings"
)

// getCutOutFilePath 子图输出相对路径
func getCutOutFilePath(dbid string, subjectId int, orgId int, roomEncode string, itemId int, candidateNo string, imageType string) string {
	return "/CutImg/c" + dbid + "_" + strconv.Itoa(subjectId) + "/" + strconv.Itoa(orgId) + "/" + roomEncode + "/" + strconv.Itoa(itemId) + "/" + candidateNo + "." + imageType
}

// convertAngle OOSCut 是否启用启用oss在线切割
func convertAngle(a string, OSSCut bool) (string, error) {
	angle, err := strconv.Atoi(a)
	if err != nil {
		return "", fmt.Errorf("旋转角度 %s 值非法: %v", a, err)
	}
	if angle < -360 {
		angle = angle + 360
	}
	if angle > 360 {
		angle = angle - 360
	}

	if OSSCut {
		if angle == 90 || angle == -270 {
			angle = 90
		} else if angle == 180 || angle == -180 {
			angle = 180
		} else if angle == 270 || angle == -90 {
			angle = 270
		} else {
			angle = 0
		}
	} else {
		if angle == 90 || angle == -270 {
			angle = 270
		} else if angle == 180 || angle == -180 {
			angle = 180
		} else if angle == 270 || angle == -90 {
			angle = 90
		} else {
			angle = 0
		}
	}
	return strconv.Itoa(angle), nil
}

// stringConvertOcclusionArea string转OcclusionArea
func stringConvertOcclusionArea(s string) []cgo.OcclusionArea {
	var areas []cgo.OcclusionArea
	if s != "" {
		if strings.Contains(s, ";") {
			areavalues := strings.Split(s, ";")
			for _, areavalue := range areavalues {
				values := strings.Split(areavalue, ",")
				var area cgo.OcclusionArea
				for i, v := range values {
					value, _ := strconv.Atoi(v)
					switch i {
					case 0:
						area.PosX = value
					case 1:
						area.PosY = value
					case 2:
						area.Width = value
					case 3:
						area.Height = value
					}
				}
				areas = append(areas, area)
			}
		} else {
			values := strings.Split(s, ",")
			var area cgo.OcclusionArea
			for i, v := range values {
				value, _ := strconv.Atoi(v)
				switch i {
				case 0:
					area.PosX = value
				case 1:
					area.PosY = value
				case 2:
					area.Width = value
				case 3:
					area.Height = value
				}
			}
			areas = append(areas, area)
		}
	}
	return areas
}

// reviseCutRect 校正切割区域坐标
func reviseCutRect(rect RectInfo, imageList []TaskImage, modelImageInfo []ModelImageInfo) RectInfo {
	cutRectTmp := rect
	if cutRectTmp.RectLeft == "0" || cutRectTmp.RectTop == "0" || cutRectTmp.RectWidth == "0" || cutRectTmp.RectHeight == "0" ||
		cutRectTmp.RectLeft == "" || cutRectTmp.RectTop == "" || cutRectTmp.RectWidth == "" || cutRectTmp.RectHeight == "" {
		return cutRectTmp
	}

	var modelCrossX, modelCrossY int
	var imageFrontCrossX, imageFrontCrossY, imageBackCrossX, imageBackCrossY int

	for _, imageInfo := range modelImageInfo {
		if cutRectTmp.ImageType == imageInfo.OmrType {
			modelCrossX, _ = strconv.Atoi(imageInfo.Crossx)
			modelCrossY, _ = strconv.Atoi(imageInfo.Crossy)
		}
	}

	for _, image := range imageList {
		if cutRectTmp.ImageType[1:2] == image.CardType {
			imageFrontCrossX = image.FCrossX
			imageFrontCrossY = image.FCrossY
			imageBackCrossX = image.BCrossX
			imageBackCrossY = image.BCrossY
		}
	}
	//判断黑白卡，黑白卡不转换相对位置
	if modelCrossX != 0 && modelCrossY != 0 && modelCrossX != -999 && modelCrossY != -999 {
		if cutRectTmp.ImageType[2:] == "F" {
			//图像如果没有crossx、y，不校正
			if imageFrontCrossX != 0 && imageFrontCrossY != 0 && imageFrontCrossX != -999 && imageFrontCrossY != -999 {
				rectTop, _ := strconv.Atoi(cutRectTmp.RectTop)
				rectLeft, _ := strconv.Atoi(cutRectTmp.RectLeft)
				cutRectTmp.RectLeft = strconv.Itoa(rectLeft + (imageFrontCrossX - modelCrossX))
				cutRectTmp.RectTop = strconv.Itoa(rectTop + (imageFrontCrossY - modelCrossY))
			}
		}

		if cutRectTmp.ImageType[2:] == "B" {
			//图像如果没有crossx、y，不校正
			if imageBackCrossX != 0 && imageBackCrossY != 0 && imageBackCrossX != -999 && imageBackCrossY != -999 {
				rectTop, _ := strconv.Atoi(cutRectTmp.RectTop)
				rectLeft, _ := strconv.Atoi(cutRectTmp.RectLeft)
				cutRectTmp.RectLeft = strconv.Itoa(rectLeft + (imageBackCrossX - modelCrossX))
				cutRectTmp.RectTop = strconv.Itoa(rectTop + (imageBackCrossY - modelCrossY))
			}
		}
	}

	return cutRectTmp
}

// reviseCutRect 校正遮挡区域坐标
func reviseOcclusionRect(rect []cgo.OcclusionArea, imageType string, imageList []TaskImage, modelImageInfo []ModelImageInfo) []cgo.OcclusionArea {
	occlusionRectTmp := rect

	var modelCrossX, modelCrossY int
	var imageFrontCrossX, imageFrontCrossY, imageBackCrossX, imageBackCrossY int

	for _, imageInfo := range modelImageInfo {
		if imageType == imageInfo.OmrType[1:] {
			modelCrossX, _ = strconv.Atoi(imageInfo.Crossx)
			modelCrossY, _ = strconv.Atoi(imageInfo.Crossy)
		}
	}

	for _, image := range imageList {
		if imageType[0:1] == image.CardType {
			imageFrontCrossX = image.FCrossX
			imageFrontCrossY = image.FCrossY
			imageBackCrossX = image.BCrossX
			imageBackCrossY = image.BCrossY
		}
	}

	//判断黑白卡，黑白卡不转换相对位置
	if modelCrossX != 0 && modelCrossY != 0 && modelCrossX != -999 && modelCrossY != -999 {
		if imageType[1:] == "F" {
			//图像如果没有crossx、y，不校正
			if imageFrontCrossX != 0 && imageFrontCrossY != 0 && imageFrontCrossX != -999 && imageFrontCrossY != -999 {
				for i, occlusionRect := range occlusionRectTmp {
					if occlusionRect.PosX != 0 && occlusionRect.PosY != 0 && occlusionRect.Width != 0 && occlusionRect.Height != 0 {
						rectPosX := occlusionRect.PosX
						rectPosY := occlusionRect.PosY
						occlusionRectTmp[i].PosX = rectPosX + (imageFrontCrossX - modelCrossX)
						occlusionRectTmp[i].PosY = rectPosY + (imageFrontCrossY - modelCrossY)
					}
				}
			}
		}

		if imageType[1:] == "B" {
			//图像如果没有crossx、y，不校正
			if imageBackCrossX != 0 && imageBackCrossY != 0 && imageBackCrossX != -999 && imageBackCrossY != -999 {
				for i, occlusionRect := range occlusionRectTmp {
					if occlusionRect.PosX != 0 && occlusionRect.PosY != 0 && occlusionRect.Width != 0 && occlusionRect.Height != 0 {
						rectPosX := occlusionRect.PosX
						rectPosY := occlusionRect.PosY
						occlusionRectTmp[i].PosX = rectPosX + (imageBackCrossX - modelCrossX)
						occlusionRectTmp[i].PosY = rectPosY + (imageBackCrossY - modelCrossY)
					}

				}
			}
		}
	}
	return occlusionRectTmp
}

/*
GetOptionRecogItem 获取考生选做题识别结果

	modeOptionalItemNo:模板选做题编号串 eg:33,34;56,57
	itemOptionalStr:题目选做题号编号串 eg:33,34
	itemOptionalRule:题目选做题规则 eg:2/1 代表2选1
	candidateOptionItem:考生选做题识别结果总串 eg:01,00
*/
func getOptionRecogItem(modeOptionalItemStr, itemOptionalStr, itemOptionalRule, optionalRecogStr string) (string, error) {
	var modeOptionalStr, optionalItemRecogStr, recogResultStr string
	if !strings.Contains(modeOptionalItemStr, itemOptionalStr) {
		return "", fmt.Errorf("选做题编号%s在模版选做题编号串%s中不存在", modeOptionalItemStr, itemOptionalStr)
	}

	if itemOptionalRule == "" {
		return "", fmt.Errorf("编号为%s的选做题未设置选做规则", itemOptionalStr)
	}

	if optionalRecogStr == "" {
		return "", fmt.Errorf("考生未识别选做题识别点")
	}

	if strings.Contains(modeOptionalItemStr, ";") {
		modeOptionalItemList := strings.Split(modeOptionalItemStr, ";")
		optionRecogList := strings.Split(optionalRecogStr, ",")
		for index, modeOptionalItem := range modeOptionalItemList {
			if modeOptionalItem == itemOptionalStr {
				modeOptionalStr = modeOptionalItemList[index]
				optionalItemRecogStr = optionRecogList[index]
			}
		}
	} else {
		modeOptionalStr = modeOptionalItemStr
		optionalItemRecogStr = optionalRecogStr
	}

	if optionalItemRecogStr == "+" {
		return "-1", nil
	}

	if strings.Contains(modeOptionalStr, ",") {
		itemStrList := strings.Split(modeOptionalStr, ",")
		for index, itemStr := range itemStrList {
			//将string转为切片遍历
			optionalItemRecogRune := []rune(optionalItemRecogStr)
			if ifRecog, _ := middleware.StringToBool(string(optionalItemRecogRune[index])); ifRecog {
				if recogResultStr == "" {
					recogResultStr = itemStr
				} else {
					recogResultStr = recogResultStr + "," + itemStr
				}
			}
		}
	} else {
		recogResultStr = "-1"
	}
	//如识别结果个数与选做题选做个数不一致，生成判别任务
	rule := strings.Split(itemOptionalRule, "/")
	selectCount, _ := strconv.Atoi(rule[1])
	if strings.Count(recogResultStr, ",")+1 != selectCount {
		recogResultStr = "-1"
	}
	return recogResultStr, nil
}
