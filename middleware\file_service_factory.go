package middleware

import (
	"fmt"
	"strconv"
	"strings"
)

// FileServiceType 文件服务类型
type FileServiceType string

const (
	FileServiceApache FileServiceType = "apache"
	FileServiceMinIO  FileServiceType = "minio"
)

// FileServiceConfig 文件服务配置
type FileServiceConfig struct {
	ServiceType FileServiceType
	// Apache配置
	ApacheBaseURL string
	// MinIO配置
	MinIOEndpoint   string
	MinIOAccessKey  string
	MinIOSecretKey  string
	MinIOBucketName string
	MinIOUseSSL     bool
}

// NewFileService 根据配置创建文件服务实例
func NewFileService(config FileServiceConfig) (FileService, error) {
	switch config.ServiceType {
	case FileServiceApache:
		return NewApacheFileClient(config.ApacheBaseURL), nil
	case FileServiceMinIO:
		minioConfig := MinIOConfig{
			Endpoint:   config.MinIOEndpoint,
			AccessKey:  config.MinIOAccessKey,
			SecretKey:  config.MinIOSecretKey,
			BucketName: config.MinIOBucketName,
			UseSSL:     config.MinIOUseSSL,
		}
		return NewMinIOFileClient(minioConfig)
	default:
		return nil, fmt.Errorf("不支持的文件服务类型: %s", config.ServiceType)
	}
}

// ParseFileServiceType 解析文件服务类型字符串
func ParseFileServiceType(typeStr string) FileServiceType {
	switch strings.ToLower(typeStr) {
	case "minio":
		return FileServiceMinIO
	case "apache", "":
		return FileServiceApache
	default:
		return FileServiceApache // 默认使用Apache
	}
}

// CreateFileServiceFromSubjectConfig 从科目配置创建文件服务
// 这个函数需要根据系统配置来决定使用哪种文件服务
func CreateFileServiceFromSubjectConfig(imgUpDownLoadHeader string, fileStoreType string, minioConfig *MinIOConfig) (FileService, error) {
	serviceType := ParseFileServiceType(fileStoreType)

	switch serviceType {
	case FileServiceMinIO:
		if minioConfig == nil {
			return nil, fmt.Errorf("MinIO配置不能为空")
		}
		return NewMinIOFileClient(*minioConfig)
	case FileServiceApache:
		fallthrough
	default:
		return NewApacheFileClient(imgUpDownLoadHeader), nil
	}
}

// GetMinIOConfigFromSystemConfig 从系统配置中获取MinIO配置
// 这个函数需要根据实际的配置键来实现
func GetMinIOConfigFromSystemConfig(configs map[string]string) (*MinIOConfig, error) {
	endpoint, ok := configs["minio.endpoint"]
	if !ok || endpoint == "" {
		return nil, fmt.Errorf("MinIO endpoint未配置")
	}

	accessKey, ok := configs["minio.access.key"]
	if !ok || accessKey == "" {
		return nil, fmt.Errorf("MinIO access key未配置")
	}

	secretKey, ok := configs["minio.secret.key"]
	if !ok || secretKey == "" {
		return nil, fmt.Errorf("MinIO secret key未配置")
	}

	bucketName, ok := configs["minio.bucket.name"]
	if !ok || bucketName == "" {
		return nil, fmt.Errorf("MinIO bucket name未配置")
	}

	useSSLStr, ok := configs["minio.use.ssl"]
	useSSL := false
	if ok && useSSLStr != "" {
		var err error
		useSSL, err = strconv.ParseBool(useSSLStr)
		if err != nil {
			return nil, fmt.Errorf("MinIO SSL配置格式错误: %w", err)
		}
	}

	return &MinIOConfig{
		Endpoint:   endpoint,
		AccessKey:  accessKey,
		SecretKey:  secretKey,
		BucketName: bucketName,
		UseSSL:     useSSL,
	}, nil
}
