package config

import (
	"fmt"
	"net"
	"runtime"
	"strings"
)

// isVirtualOrBluetoothInterface 判断是否为虚拟、Docker 或蓝牙接口
func isVirtualOrBluetoothInterface(name, mac string, isWindows bool) bool {
	// 虚拟、Docker 和蓝牙接口名称前缀
	virtualPrefixes := []string{
		"docker", "veth", "br-", "vnet", "vmnet", "vboxnet", // Docker 和虚拟机
		"tap", "tun", "bond", "sit", // VPN/隧道/其他
		"bnep", "bluetooth", "蓝牙", // 蓝牙接口
	}
	if isWindows {
		virtualPrefixes = append(virtualPrefixes, "virtual", "loopback", "microsoft", "hyper-v")
	} else {
		virtualPrefixes = append(virtualPrefixes, "lo")
	}

	for _, prefix := range virtualPrefixes {
		if strings.Contains(name, prefix) {
			return true
		}
	}

	// 虚拟机 MAC 地址前缀
	virtualMacPrefixes := []string{
		"00:05:69", // VMware
		"00:0c:29", // VMware
		"00:50:56", // VMware
		"08:00:27", // Burr VirtualBox
	}
	for _, prefix := range virtualMacPrefixes {
		if strings.HasPrefix(mac, prefix) {
			return true
		}
	}

	return false
}

// GetMacAddresses 获取本机有效 MAC 地址，过滤虚拟机、Docker 和蓝牙接口
func GetMacAddresses(single bool) ([]string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("failed to get network interfaces: %v", err)
	}

	var macAddresses []string
	isWindows := runtime.GOOS == "windows"

	for _, iface := range interfaces {
		// 跳过无效或回环接口
		if iface.HardwareAddr == nil || len(iface.HardwareAddr) == 0 || iface.Flags&net.FlagLoopback != 0 {
			continue
		}

		name := strings.ToLower(iface.Name)
		mac := iface.HardwareAddr.String()

		// 过滤虚拟、Docker 或蓝牙接口
		if isVirtualOrBluetoothInterface(name, mac, isWindows) {
			//log.Printf("Skipping interface: %s (MAC: %s)", iface.Name, mac)
			continue
		}

		macAddresses = append(macAddresses, mac)
		//log.Printf("Found valid interface: %s (MAC: %s)", iface.Name, mac)

		// 如果只需要单个 MAC 地址，立即返回
		if single && len(macAddresses) > 0 {
			return macAddresses, nil
		}
	}

	if len(macAddresses) == 0 {
		return nil, fmt.Errorf("no valid MAC addresses found")
	}

	return macAddresses, nil
}
