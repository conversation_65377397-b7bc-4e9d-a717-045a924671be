package config

import (
	"os"
	"path/filepath"
	"testing"
)

// TestPollingConfigDefaults 测试轮询配置默认值
func TestPollingConfigDefaults(t *testing.T) {
	// 备份原始配置
	originalConfig := BaseConfig

	// 重置配置
	BaseConfig = Config{}

	// 创建临时配置文件（不包含轮询开关）
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "HytCrypto.ini")
	
	configContent := `[gceLOG]
LogLevel=3

[SetIP]
MainIP=*********
`
	
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("创建临时配置文件失败: %v", err)
	}

	// 设置临时执行目录
	BaseConfig.ExecDir = tempDir

	// 加载配置
	err = LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证默认值
	if !BaseConfig.IniConfig.CutPollingEnabled {
		t.<PERSON>("期望 CutPollingEnabled 默认为 true，实际为 %v", BaseConfig.IniConfig.CutPollingEnabled)
	}

	if !BaseConfig.IniConfig.RecogPollingEnabled {
		t.Errorf("期望 RecogPollingEnabled 默认为 true，实际为 %v", BaseConfig.IniConfig.RecogPollingEnabled)
	}

	// 恢复原始配置
	BaseConfig = originalConfig
}

// TestPollingConfigExplicitValues 测试明确设置的轮询配置值
func TestPollingConfigExplicitValues(t *testing.T) {
	// 备份原始配置
	originalConfig := BaseConfig

	// 重置配置
	BaseConfig = Config{}

	// 创建临时配置文件（包含轮询开关）
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "HytCrypto.ini")
	
	configContent := `[gceLOG]
LogLevel=3

[SetIP]
MainIP=*********

CutPollingEnabled=false
RecogPollingEnabled=true
`
	
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("创建临时配置文件失败: %v", err)
	}

	// 设置临时执行目录
	BaseConfig.ExecDir = tempDir

	// 加载配置
	err = LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证明确设置的值
	if BaseConfig.IniConfig.CutPollingEnabled {
		t.Errorf("期望 CutPollingEnabled 为 false，实际为 %v", BaseConfig.IniConfig.CutPollingEnabled)
	}

	if !BaseConfig.IniConfig.RecogPollingEnabled {
		t.Errorf("期望 RecogPollingEnabled 为 true，实际为 %v", BaseConfig.IniConfig.RecogPollingEnabled)
	}

	// 恢复原始配置
	BaseConfig = originalConfig
}

// TestPollingConfigBothDisabled 测试两个轮询都禁用的情况
func TestPollingConfigBothDisabled(t *testing.T) {
	// 备份原始配置
	originalConfig := BaseConfig

	// 重置配置
	BaseConfig = Config{}

	// 创建临时配置文件（两个轮询都禁用）
	tempDir := t.TempDir()
	configPath := filepath.Join(tempDir, "HytCrypto.ini")
	
	configContent := `[gceLOG]
LogLevel=3

[SetIP]
MainIP=*********

CutPollingEnabled=false
RecogPollingEnabled=false
`
	
	err := os.WriteFile(configPath, []byte(configContent), 0644)
	if err != nil {
		t.Fatalf("创建临时配置文件失败: %v", err)
	}

	// 设置临时执行目录
	BaseConfig.ExecDir = tempDir

	// 加载配置
	err = LoadConfig()
	if err != nil {
		t.Fatalf("加载配置失败: %v", err)
	}

	// 验证两个轮询都被禁用
	if BaseConfig.IniConfig.CutPollingEnabled {
		t.Errorf("期望 CutPollingEnabled 为 false，实际为 %v", BaseConfig.IniConfig.CutPollingEnabled)
	}

	if BaseConfig.IniConfig.RecogPollingEnabled {
		t.Errorf("期望 RecogPollingEnabled 为 false，实际为 %v", BaseConfig.IniConfig.RecogPollingEnabled)
	}

	// 恢复原始配置
	BaseConfig = originalConfig
}

// TestPollingConfigValidation 测试轮询配置验证
func TestPollingConfigValidation(t *testing.T) {
	tests := []struct {
		name           string
		configContent  string
		expectedCut    bool
		expectedRecog  bool
	}{
		{
			name: "默认配置（无轮询设置）",
			configContent: `[gceLOG]
LogLevel=3
[SetIP]
MainIP=*********`,
			expectedCut:   true,
			expectedRecog: true,
		},
		{
			name: "仅切割轮询启用",
			configContent: `[gceLOG]
LogLevel=3
[SetIP]
MainIP=*********
CutPollingEnabled=true
RecogPollingEnabled=false`,
			expectedCut:   true,
			expectedRecog: false,
		},
		{
			name: "仅识别轮询启用",
			configContent: `[gceLOG]
LogLevel=3
[SetIP]
MainIP=*********
CutPollingEnabled=false
RecogPollingEnabled=true`,
			expectedCut:   false,
			expectedRecog: true,
		},
		{
			name: "两个轮询都启用",
			configContent: `[gceLOG]
LogLevel=3
[SetIP]
MainIP=*********
CutPollingEnabled=true
RecogPollingEnabled=true`,
			expectedCut:   true,
			expectedRecog: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 备份原始配置
			originalConfig := BaseConfig

			// 重置配置
			BaseConfig = Config{}

			// 创建临时配置文件
			tempDir := t.TempDir()
			configPath := filepath.Join(tempDir, "HytCrypto.ini")
			
			err := os.WriteFile(configPath, []byte(tt.configContent), 0644)
			if err != nil {
				t.Fatalf("创建临时配置文件失败: %v", err)
			}

			// 设置临时执行目录
			BaseConfig.ExecDir = tempDir

			// 加载配置
			err = LoadConfig()
			if err != nil {
				t.Fatalf("加载配置失败: %v", err)
			}

			// 验证配置值
			if BaseConfig.IniConfig.CutPollingEnabled != tt.expectedCut {
				t.Errorf("CutPollingEnabled: 期望 %v，实际 %v", 
					tt.expectedCut, BaseConfig.IniConfig.CutPollingEnabled)
			}

			if BaseConfig.IniConfig.RecogPollingEnabled != tt.expectedRecog {
				t.Errorf("RecogPollingEnabled: 期望 %v，实际 %v", 
					tt.expectedRecog, BaseConfig.IniConfig.RecogPollingEnabled)
			}

			// 恢复原始配置
			BaseConfig = originalConfig
		})
	}
}
