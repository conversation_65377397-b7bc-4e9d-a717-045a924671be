package cut

import (
	"fmt"
	"imgProcServe/job"
	"strconv"
)

type Subject struct {
	ExamId        int    `json:"examId"`
	SubjectId     int    `json:"subjectId"`
	SubjectCode   string `json:"subjectCode"`
	SubjectName   string `json:"examName"`
	ModelFileName string `json:"modelFileName"`
}

type SubjectInfo struct {
	ExamId          int              `json:"examId"`
	SubjectId       int              `json:"subjectId"`
	SubjectName     string           `json:"subjectName"`
	ModelId         int              `json:"modelId"`   //默认模板ID
	GeneralCardFlag string           `json:"cardType"`  //0专卡，1通卡
	OptItemNo       string           `json:"optItemNo"` //选做题串
	MaxCardModelCnt int              //模板最大卡数
	SubjectConfig   SubjectConfig    //科目参数
	ModelImageInfo  []ModelImageInfo //模版信息
	StartItemList   []ItemInfo       //开启切割题目信息
}

// 获取可切割科目列表
func getCutSubjectList(examId int) ([]Subject, error) {
	params := make(map[string]string)
	params["examId"] = strconv.Itoa(examId)
	params["filterHasTask"] = "true"
	params["pageNumEx"] = "1"
	params["pageSizeEx"] = "100000"
	return job.FetchData[[]Subject, []Subject](
		job.NewHttpClient(),
		job.POST,
		"/scan/examSubject/getSubjectListForCut",
		params,
		nil)
}

// 获取科目信息
func getCutSubjectInfo(subjectId int) ([]*SubjectInfo, error) {
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectId)
	subjectInfoList, err := job.FetchData[[]*SubjectInfo, []*SubjectInfo](
		job.NewHttpClient(),
		job.POST,
		"/scan/examSubject/list",
		params,
		nil)

	if err != nil {
		return nil, err
	}

	for index, subjectInfo := range subjectInfoList {
		subjectInfoList[index].SubjectConfig, err = getSubjectCutConfig(subjectInfo.SubjectId)
		if err != nil {
			return nil, err
		}

		subjectInfoList[index].MaxCardModelCnt, err = getMaxCardModeCnt(subjectInfo.SubjectId)
		if err != nil {
			return nil, err
		}
	}

	return subjectInfoList, nil
}

// ItemInfo 题目信息
type ItemInfo struct {
	ItemId            int    `json:"itemId"`
	Stitich           int    `json:"stitich"`           //拼接方式(横拼接、竖拼接)
	ImgType           string `json:"imgType"`           //图像格式(jpg、gif)
	JudgeFlag         string `json:"judgeFlag"`         //是否判别题
	OptSelectHasOmr   string `json:"optSelectHasOmr"`   //选做题是否有识别点
	OptSelectItemType string `json:"optSelectItemType"` //多选题类型：如3/1为三选一，4/2为四选二
	OptSelectItemStr  string
	CutRectList       []RectInfo
}

// RectInfo 对应ItemId的切割块信息
type RectInfo struct {
	ItemCutId  int    `json:"sortNo"`
	ImageType  string `json:"cardType"`   //卷型卡型正反面 eg:A1F A2B
	RectTop    string `json:"rectLeft"`   //切割左上角点Posy,后端数据对应错误
	RectLeft   string `json:"rectTop"`    //切割左上角点Posx，后端数据对应错误
	RectWidth  string `json:"rectWidth"`  //切割区域宽
	RectHeight string `json:"rectHeight"` //切割区域高
	ImgCutMD5  string `json:"imgCutMD5"`  //切割块信息MD5
}

func getStartCutSubjectItemList(subject SubjectInfo) ([]ItemInfo, error) {
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subject.SubjectId)
	params["isStartCut"] = "1"
	params["optSelectFlag"] = "0"

	itemList, err := job.FetchData[[]ItemInfo, []ItemInfo](
		job.NewHttpClient(),
		job.GET,
		"/base/item/querySubjectItemList",
		params,
		nil)

	if err != nil {
		return nil, err
	}
	if len(itemList) == 0 {
		return itemList, nil
	}

	for index, item := range itemList {
		var cutRects []RectInfo
		//获取题目的切割块信息
		params = make(map[string]string)
		params["subjectId"] = strconv.Itoa(subject.SubjectId)
		if subject.GeneralCardFlag == "0" { //专卡
			params["itemId"] = strconv.Itoa(item.ItemId)
		}
		cutRects, err = job.FetchData[[]RectInfo, []RectInfo](
			job.NewHttpClient(),
			job.GET,
			"/scan/ItemCut/selectItemCutInfoList",
			params,
			nil)

		if err != nil {
			return nil, err
		}
		if len(cutRects) > 0 {
			itemList[index].CutRectList = cutRects
		}
		if item.JudgeFlag == "1" {
			//获取选做题组信息
			params = make(map[string]string)
			params["subjectId"] = strconv.Itoa(subject.SubjectId)
			params["judgeItemId"] = strconv.Itoa(item.ItemId)
			httpClient := job.NewHttpClient()
			_, err = job.FetchData[string, string](
				httpClient,
				job.GET,
				"/scan/optSelect/getModelItemNoListByJudgeItemId",
				params,
				nil)

			if err != nil {
				return nil, err
			}
			if httpClient.HttpClient.Msg != "" {
				itemList[index].OptSelectItemStr = httpClient.HttpClient.Msg
			}
		}
	}

	return itemList, nil
}

// 获取切割前科目所有信息
func getCutSubjectAllInfo(subjectId int) ([]*SubjectInfo, error) {
	subjectInfoList, err := getCutSubjectInfo(subjectId)
	if err != nil {
		return nil, fmt.Errorf("获取科目%d信息失败:%w", subjectId, err)
	}
	for i := 0; i < len(subjectInfoList); i++ {
		subjectInfoList[i].SubjectConfig, err = getSubjectCutConfig(subjectInfoList[i].SubjectId)
		if err != nil {
			return nil, fmt.Errorf("获取科目%d参数失败:%w", subjectId, err)
		}
		subjectInfoList[i].MaxCardModelCnt, err = getMaxCardModeCnt(subjectInfoList[i].SubjectId)
		if err != nil {
			return nil, fmt.Errorf("获取科目%d最大卡数失败:%w", subjectId, err)
		}
		subjectInfoList[i].ModelImageInfo, err = getModelInfo(subjectId, subjectInfoList[i].ModelId, subjectInfoList[i].GeneralCardFlag)
		if err != nil {
			return nil, fmt.Errorf("获取科目%d模版卡信息失败:%w", subjectId, err)
		}
		subjectInfoList[i].StartItemList, err = getStartCutSubjectItemList(*subjectInfoList[i])
		if err != nil {
			return nil, fmt.Errorf("获取科目%d开启切割题块信息失败:%w", subjectId, err)
		}
	}
	return subjectInfoList, nil
}
