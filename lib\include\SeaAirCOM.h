// seaair.h
#ifndef SEAAIRCOM_H
#define SEAAIRCOM_H

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _WIN32
#define SEAAIR_API __declspec(dllimport)
#else
#define SEAAIR_API
#endif



//char** OutString
//unsigned char* pInImgF = NULL
//const int Width = 0
//const int Height = 0
//const int BitCount = 0
//const int ImgType = 0
//unsigned char* pInImgB = NULL
SEAAIR_API void SeaAirMemArrFree_go(const char* ArrPtr);

#ifdef __cplusplus
}
#endif

#endif