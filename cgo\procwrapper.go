package cgo

/*
#cgo LDFLAGS: -L${SRCDIR}/../lib/windows/amd64 -lSeaAirPRO
#include "../lib/include/SeaAirPRO.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
*/
import "C"
import (
	"encoding/json"
	"fmt"
	"log"
	"unsafe"
)

type DllJson struct {
	Func    string      `json:"func"`
	RatioTh int         `json:"RatioTh"`
	InImgs  []DllInImg  `json:"InImgs"`
	OutImgs []DllOutImg `json:"OutImgs"`
}

// DllInImg 输入图像数组
type DllInImg struct {
	ImagePath          string          `json:"img"`
	OcclusionAreaRects []OcclusionArea `json:"coverRects"` //遮挡区域
	RotateAngle        int             `json:"angle"`      //图像旋转角度
	ModelH             int             `json:"ModelH"`     //模板图像高
	ModelW             int             `json:"ModelW"`     //模板图像宽
}

// OcclusionArea 遮挡区域
type OcclusionArea struct {
	PosX   int `json:"x"`
	PosY   int `json:"y"`
	Width  int `json:"w"`
	Height int `json:"h"`
}

// DllOutImg 输出图像数组
type DllOutImg struct {
	HorV   string        `json:"HorV"` //拼接方向:”H” 水平;”V”垂直;
	OutImg string        `json:"OutImg"`
	InImgs []DllOutInImg `json:"InImgs"`
	Align  int           `json:"align"` //拼接对齐方式: 1:居中对齐,0:垂直时左对齐,水平时上对齐 没有右对齐
	ImgUrl string
}

type DllOutInImg struct {
	ImagePath string       `json:"img"`
	Rects     []DllCutArea `json:"rects"`
}
type DllCutArea struct {
	Height int `json:"h"`
	Width  int `json:"w"`
	PosX   int `json:"x"`
	PosY   int `json:"y"`
}

// CutJson 切割输入Json
type CutJson struct {
	Function        string                `json:"func"`    //功能
	Ratio           int                   `json:"RatioTh"` //放缩比例阈值
	CutSourceImages []CutSourceImagesJson `json:"InImgs"`  //输入图像数组
	CutOutputImages []CutOutputImagesJson `json:"OutImgs"` //输出图像数组
}

// CutSourceImagesJson 输入图像数组
type CutSourceImagesJson struct {
	SourceImagePath           string                          `json:"img"`        //输入图像路径
	ImageRotateAngle          int                             `json:"angle"`      //旋转角度
	ModelHeight               int                             `json:"ModelH"`     //模板图像高
	ModelWidth                int                             `json:"ModelW"`     //模版图像宽
	CutSourceImagesCoverRects []CutSourceImagesCoverRectsJson `json:"coverRects"` //遮挡区域数组
}

// CutSourceImagesCoverRectsJson 遮挡区域
type CutSourceImagesCoverRectsJson struct {
	Height int `json:"h"` //高
	Width  int `json:"w"` //宽
	Left   int `json:"x"` //x
	Top    int `json:"y"` //y
}

// CutOutputImagesJson 输出图像数组
type CutOutputImagesJson struct {
	CutInImages      []CutInImagesJson `json:"InImgs"` //切割块信息数组
	OutputImagePaths string            `json:"OutImg"` //输出图像路径
	JoinAlignType    int               `json:"align"`  //拼接对齐方式: 1:居中对齐, 0:垂直时左对齐,水平时上对齐 没有右对齐
	JoinDirect       string            `json:"HorV"`   //拼接方向: ”H” 水平, ”V” 垂直
}

// CutInImagesJson 切割块信息（源图像与切割区域）
type CutInImagesJson struct {
	InImagePath string                `json:"img"`   //切割块对应源图像路径
	CutRects    []CutInImageRectsJson `json:"rects"` //切割区域数组
}

// CutInImageRectsJson 切割区域
type CutInImageRectsJson struct {
	Height int `json:"h"` //高
	Width  int `json:"w"` //宽
	Left   int `json:"x"` //x
	Top    int `json:"y"` //y
}

// ImgCut 图像切割
func ImgCut(cjson DllJson) error {
	jsonData, err := json.Marshal(cjson)
	if err != nil {
		return fmt.Errorf("转换切割Json失败: %v", err)
	}
	cInString := C.CString(string(jsonData))
	defer C.free(unsafe.Pointer(cInString))

	// Go 变量，类型为 C 指针，初始值为 nil
	var cOutString *C.char
	defer C.free(unsafe.Pointer(cOutString))

	result, err := C.SeaAirPRO(cInString, &cOutString)
	if err != nil {
		return fmt.Errorf("切割失败: %v", err)
	}

	// 检查返回值
	if int(result) != 0 {

		return fmt.Errorf("切割失败，错误代码: %v", int(result))
	} else {
		log.Printf("切割成功: %v", int(result))
	}
	return nil
}
