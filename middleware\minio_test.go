package middleware

import (
	"log"
	"testing"
)

func Test_MinIOUploadFile(t *testing.T) {
	// MinIO测试配置
	config := MinIOConfig{
		Endpoint:   "*********:20900",
		AccessKey:  "hytadmin",
		SecretKey:  "sky@2023",
		BucketName: "hyt-img",
		UseSSL:     false,
	}

	fileService, err := NewMinIOFileClient(config)
	if err != nil {
		log.Fatal("创建MinIO客户端失败:", err)
	}

	localFilePath := "C:\\Users\\<USER>\\Desktop\\test_auto\\**********.TIF"
	remoteFilePath := "aaaaaaa/CutImg/**********.Tif"

	err = fileService.UploadFile(localFilePath, remoteFilePath)
	if err != nil {
		log.Fatal("上传失败:", err)
	}

	log.Println("上传成功")
}

func Test_MinIODownloadFile(t *testing.T) {
	// MinIO测试配置
	config := MinIOConfig{
		Endpoint:   "localhost:9000",
		AccessKey:  "minioadmin",
		SecretKey:  "minioadmin",
		Bucket<PERSON>ame: "test-bucket",
		UseSSL:     false,
	}

	fileService, err := NewMinIOFileClient(config)
	if err != nil {
		log.Fatal("创建MinIO客户端失败:", err)
	}

	// 测试从完整URL下载
	remoteFile := "http://localhost:9000/test-bucket/aaaaaaa/CutImg/00000003.Tif"
	localFile := "F:\\test\\downloaded_00000003.Tif"

	err = fileService.DownloadFile(remoteFile, localFile)
	if err != nil {
		log.Fatal("下载失败:", err)
	}

	log.Println("下载成功")
}

func Test_FileServiceFactory(t *testing.T) {
	// 测试Apache文件服务
	apacheConfig := FileServiceConfig{
		ServiceType:   FileServiceApache,
		ApacheBaseURL: "http://*********:20501",
	}

	apacheService, err := NewFileService(apacheConfig)
	if err != nil {
		log.Fatal("创建Apache文件服务失败:", err)
	}
	log.Printf("Apache文件服务创建成功: %T", apacheService)

	// 测试MinIO文件服务
	minioConfig := FileServiceConfig{
		ServiceType:     FileServiceMinIO,
		MinIOEndpoint:   "localhost:9000",
		MinIOAccessKey:  "minioadmin",
		MinIOSecretKey:  "minioadmin",
		MinIOBucketName: "test-bucket",
		MinIOUseSSL:     false,
	}

	minioService, err := NewFileService(minioConfig)
	if err != nil {
		log.Fatal("创建MinIO文件服务失败:", err)
	}
	log.Printf("MinIO文件服务创建成功: %T", minioService)
}
