;--系统设置
[SysSet]
;信息输出设置
;输出每个主要功能耗时
LogTime=0
;输出输出输入的json
LogInfo=0
;输出某些主要的调试信息
DebugOut=0

;--业务设置
[BusinessSet]
;--检测条形码二维码灰度级和旋转角度步进
GrayThStep=5
AngleMaxQR=45
AngleThStep=5
OCRQR=1
;--停止扫描角度阈值,高于此值将停止扫描
StopScanAngleTh=3
;--自动校正角度阈值,高于此值,低于StopScanAngleTh题卡将进行整体校正,对于标卡则提示需要重新获取图像
CorrectAngleTh=1
;--旋转整体校正角度阈值,高于此值,低于CorrectAngleTh将进行旋转校正,对于标卡也需要重新获取图像
RotateAngleTh=0.5
;图像的最大边长限制,默认1024,必须32的整数倍,最小960,当图像实际大小超过此值时能起作用,但超过1600以上,可能会导致内存不足,识别失败,太小识别率低,
MaxSideLenTh=960
;扫描图像歪斜校正时的旋转图像模式:
;0:保证图像大小不变,此时如果旋转角度较大,边角上内容可能会丢失
;1:保证图像内容不丢失,此时图像会变大,旋转角度越大,图像越大
RotateImageMode=0
;是否采用QR判断方位
IsQROriPos=0
;手写考号
HWDigitCPDis=3
HWDigitBetweenDis=0.3
;空白检测时误差半径
BlankDetectError=6
;标卡是否需要校正验证特征,0无需验证,1需要验证
TranCorrectNumVerfy=1
;标卡是否需要直线交点作为定位点,0不需要,1需要
LineInterPToFixPs=0
;jpg格式子图质量
jpgQuality=90
;--开发测试使用,发布版删除即可
[devtest]
OutFileTest=1

;Omr识别参数
[BWRecParam]
;为1时图像必须是正向的
localKW=0
;localKW==1时有效
locateMatchRatio=70
;客观题识别算法,0,新锐为主,1考网为主,2仅新锐,3仅考网
kgtRec=0
RecParamStatisAllArea=0
;--Omr起始灰度阈值
OmrGrayTh=236
;--Omr起始灰度阈值步进值,从OmrGrayTh开始交替增加或减小OmrGrayThStep
OmrGrayThStep=5
;兼容的歪斜角度,最大为5
OmrSkewAngle=7.5
pageRotateAngle=0
whiteHaltRatio=5.0
;仅有一部分客观题需要识别,仅localKW==0时有效,要求标卡当前区域的所有填涂框都必须能找得到
OnlyAreaPart=0
;正常的区域包含的小题数量,仅OnlyAreaPart=1时有效
AreaLineCnt=5
//页面分析时的灰度阈值
paperOmrGrayTh=190
paperOmrDiff=8
filterNotSuitDiff=7
;其他参数
[OtherSet]
blackWMaxTh=35
XorYLenlimitMinTh=200
WHRateMaxThScan=4.0
WHRateMaxTh=4.5
CoverFindRadius=5
CoverContinueCntTh=3

BlackGrayTh=150
StopScanBlockEdgeW=50
RangeDiff=150
FixPWHRateMin=0.6
FixPWHRateMax=1.2
FixPWHExt=2
FixPFindLoose=1
ScanLineDetectHeightTh=200
ScanLineDetectMargin=10
ScanLineDetectGrayDiffTh=2
[ColorFilter]
;红/蓝/绿色过滤的hsv最小值,最大值分别为相应最大值
hMinRed=160
sMinRed=43
vMinRed=150

hMinBlue=160
sMinBlue=43
vMinBlue=150

hMinGreen=160
sMinGreen=43
vMinGreen=150


