package recog

import (
	"archive/zip"
	"bytes"
	"fmt"
	"imgProcServe/cgo"
	"imgProcServe/job"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

func initModel(subjectInfo *SubjectInfo) error {
	modeFile, err := downLoadModel(subjectInfo)
	if err != nil {
		return fmt.Errorf("下载模版失败：%w", err)
	}
	err = cgo.ModelInit(modeFile)
	if err != nil {
		return fmt.Errorf("初始化模版失败：%w", err)
	}
	return nil
}

// 下载模版
func downLoadModel(subjectInfo *SubjectInfo) (string, error) {
	params := make(map[string]string)
	params["modelId"] = strconv.Itoa(subjectInfo.ModelId)
	modelData, err := job.FetchFileData(
		job.NewHttpClient(),
		job.GET,
		"/scan/model/downLoadTemplate/byModelId",
		params,
		nil)

	if err != nil {
		return "", fmt.Errorf("failed to download model data: %w", err)
	}
	if len(modelData) == 0 {
		return "", fmt.Errorf("received empty model data for modelId: %d", subjectInfo.ModelId)
	}
	// 准备文件路径
	modelTempDir := subjectInfo.SubRecogTempDir + "\\model"
	fileName := fmt.Sprintf("recogModel_%d.zip", subjectInfo.ModelId) // 添加modelId避免冲突
	filePath := filepath.Join(modelTempDir, fileName)

	//  确保目录存在
	if err := os.MkdirAll(modelTempDir, 0755); err != nil {
		return "", fmt.Errorf("failed to create temp directory: %w", err)
	}

	//  创建文件
	out, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to create file %s: %w", filePath, err)
	}

	defer func() {
		if closeErr := out.Close(); closeErr != nil {
			// 记录关闭文件时的错误，但不覆盖主要错误
			fmt.Printf("Warning: failed to close file: %v\n", closeErr)
		}
	}()

	reader := bytes.NewReader(modelData)
	bytesWritten, err := io.Copy(out, reader)
	if err != nil {
		return "", fmt.Errorf("failed to write model data to file: %w", err)
	}

	// 验证写入的数据量
	if bytesWritten != int64(len(modelData)) {
		return "", fmt.Errorf("incomplete write: expected %d bytes, wrote %d bytes", len(modelData), bytesWritten)
	}

	//  确保数据写入磁盘
	if err := out.Sync(); err != nil {
		return "", fmt.Errorf("failed to sync file to disk: %w", err)
	}

	// 解压zip文件
	if err := extractZipFile(filePath, modelTempDir); err != nil {
		return "", fmt.Errorf("failed to extract zip file: %w", err)
	}

	// 删除zip文件（可选）
	if err := os.Remove(filePath); err != nil {
		fmt.Printf("Warning: failed to remove zip file %s: %v\n", filePath, err)
	}

	// 查找.air文件
	airFilePath, err := findAirFile(modelTempDir)
	if err != nil {
		return "", fmt.Errorf("failed to find .air file: %w", err)
	}

	return airFilePath, nil
}

// extractZipFile 解压zip文件到指定目录
func extractZipFile(src, dest string) error {
	// 打开zip文件
	r, err := zip.OpenReader(src)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer func(r *zip.ReadCloser) {
		_ = r.Close()
	}(r)

	// 确保目标目录存在
	if err := os.MkdirAll(dest, 0755); err != nil {
		return fmt.Errorf("failed to create destination directory: %w", err)
	}

	// 解压每个文件
	for _, f := range r.File {
		// 构建完整的文件路径
		path := filepath.Join(dest, f.Name)

		// 检查路径安全性，防止zip slip攻击
		if !strings.HasPrefix(path, filepath.Clean(dest)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path: %s", f.Name)
		}

		if f.FileInfo().IsDir() {
			// 创建目录
			if err := os.MkdirAll(path, f.FileInfo().Mode()); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", path, err)
			}
			continue
		}

		// 确保父目录存在
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return fmt.Errorf("failed to create parent directory for %s: %w", path, err)
		}

		// 打开zip中的文件
		rc, err := f.Open()
		if err != nil {
			return fmt.Errorf("failed to open file %s in zip: %w", f.Name, err)
		}

		// 创建目标文件
		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, f.FileInfo().Mode())
		if err != nil {
			_ = rc.Close()
			return fmt.Errorf("failed to create file %s: %w", path, err)
		}

		// 复制文件内容
		_, err = io.Copy(outFile, rc)
		_ = outFile.Close()
		_ = rc.Close()

		if err != nil {
			return fmt.Errorf("failed to extract file %s: %w", f.Name, err)
		}
	}

	return nil
}

// findAirFile 在指定目录中查找.air文件
func findAirFile(dir string) (string, error) {
	var airFilePath string

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查文件扩展名是否为.air
		if !info.IsDir() && strings.ToLower(filepath.Ext(path)) == ".air" {
			airFilePath = path
			return filepath.SkipDir // 找到第一个.air文件后停止搜索
		}

		return nil
	})

	if err != nil {
		return "", fmt.Errorf("error walking directory %s: %w", dir, err)
	}

	if airFilePath == "" {
		return "", fmt.Errorf("no .air file found in directory %s", dir)
	}

	return airFilePath, nil
}
