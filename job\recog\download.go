package recog

import (
	"fmt"
)

type ImageDownLoad struct {
	CardType          string
	PreHttpPath       string
	ImageHttpPath     string
	ImageSourcePath   string
	ImageDownLoadPath string
}

func (rt *Task) DownLoad(task *Task) {
	fileService, err := createFileService(task.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader)
	if err != nil {
		task.TaskError = fmt.Errorf("创建文件服务失败：%v", err)
		return
	}

	for _, downImg := range task.ImageDownLoad {
		err := fileService.DownloadFile(downImg.PreHttpPath+downImg.ImageHttpPath, downImg.ImageDownLoadPath)
		if err != nil {
			task.TaskError = fmt.Errorf("原图下载失败：%v", err)
			return
		}
	}
}
