package cut

import (
	"fmt"
	"imgProcServe/cgo"
	"imgProcServe/internal/config"
	"imgProcServe/job"
	"math/rand"
	"os"
	"strconv"
	"sync"
	"time"
)

type Task struct {
	TaskError          error
	SubjectId          int         `json:"subjectId"`
	CandidateNo        string      `json:"candidateNo"`
	Version            int         `json:"version"`
	OrgId              int         `json:"orgId"`
	RoomId             int         `json:"roomId"`
	PaperType          string      `json:"paperType"`
	RoomEncode         string      `json:"roomEncode"`
	ScanBatchNO        string      `json:"scanBatchNO"`
	AbnormalCardFlag   string      `json:"abnormalCardFlag"`
	ImageList          []TaskImage `json:"imgList"`
	OptionalSubjectOmr string      //主观题选做OMR串
	CutTmpDir          string
	ItemCutRecord      []ItemCutRecordJson
	SubjectInfo        SubjectInfo
	CutJson            cgo.DllJson
	TaskUpdate         TaskUpdate
	ImageDownLoad      []ImageDownLoad
	ImageUpLoad        []ImageUpLoad
}

type TaskImage struct {
	FImage               string `json:"fImage"`
	BImage               string `json:"bImage"`
	ImageId              string `json:"imageId"`
	CardType             string `json:"cardType"`
	FSubjectiveOmr       string `json:"fSubjectiveOmr"`
	BSubjectiveOmr       string `json:"bSubjectiveOmr"`
	FrontOcclusionArea   string `json:"frontOcclusionArea"`
	BackOcclusionArea    string `json:"backOcclusionArea"`
	FrontOcclusionRotate string `json:"frontOcclusionRotate"`
	BackOcclusionRotate  string `json:"backOcclusionRotate"`
	FCrossX              int    `json:"fCrossX"`
	FCrossY              int    `json:"fCrossY"`
	BCrossX              int    `json:"bCrossX"`
	BCrossY              int    `json:"bCrossY"`
}

type ImageDownLoad struct {
	CardType          string
	PreHttpPath       string
	ImageHttpPath     string
	ImageSourcePath   string
	ImageDownLoadPath string
}
type ImageUpLoad struct {
	PreHttpPath           string
	ImageHttpPath         string
	UpLoadImageSourcePath string
}

// TaskUpdate 切割更新Json
type TaskUpdate struct {
	CandidateCutUpdate   CandidateCutUpdate `json:"scanCandidate"`
	CandidateItemCutList []CandidateItemCut `json:"scanCandidateItemCutList"`
}
type CandidateCutUpdate struct {
	SubjectId   string `json:"subjectId"`
	CandidateNo string `json:"candidateNo"`
	ScanBatchNo string `json:"scanBatchNo"`
	CutStatus   string `json:"cutStatus"`
	CutErrmsg   string `json:"cutErrmsg"`
	Version     string `json:"version"`
}
type CandidateItemCut struct {
	SubjectId          string `json:"subjectId"`
	CandidateNo        string `json:"candidateNo"`
	ItemId             string `json:"itemId"`
	CutImgServerIp     string `json:"cutImgServerIp"`
	Url                string `json:"url"`
	CutMd5             string `json:"cutMd5"`
	CutStatus          string `json:"cutStatus"`
	JudgeItem          string `json:"judgeItem"`
	JudgeItemId        string `json:"judgeItemId"`
	OptSelectOmrResult string `json:"optSelectOmrResult"`
	Version            string `json:"version"`
}

/*
func Cut(subject int) error {
	getTaskErrCnt := 0
	for {
		//任务获取
		cutTaskList, err := GetCutTask(subject)
		if err != nil {
			fmt.Printf("获取切割数据失败:%v\n", err)
			getTaskErrCnt++
			if getTaskErrCnt > 5 {
				break  }
		}

		if len(cutTaskList) == 0 {
			break
		}

		for _, task := range cutTaskList {
			fmt.Printf("%+v\n", task)
		}

		var wgTask sync.WaitGroup
		wgTask.Add(len(cutTaskList))
		for _, task := range cutTaskList {
			go func(task *Task) {
				defer wgTask.Done()
				//图像下载
				if task.TaskError == nil {
					task.DownLoad()
				}
				//图像处理
				if task.TaskError == nil {
					err = cgo.ImgCut(task.CutJson)
					if err != nil {
						task.TaskError = err
					}
				}
				//图像上传
				if task.TaskError == nil {
					task.UpLoad()
				}

				if task.TaskError != nil {
					task.TaskUpdate.CandidateCutUpdate.CutErrmsg = task.TaskError.Error()
					task.TaskUpdate.CandidateCutUpdate.CutStatus = "-1"
				} else {
					task.TaskUpdate.CandidateCutUpdate.CutStatus = "8"
				}

				err = task.updateTask()
				if err != nil {
					fmt.Println(err)
				}
			}(task)
		}
		wgTask.Wait()
		//当前批次结束后删除临时图像
		err = os.RemoveAll(cutTaskList[0].CutTmpDir)
		if err != nil {
			fmt.Println(err)
		}
	}
	return nil
}
*/

// GetCutTask 获取切割数据
func GetCutTask(subject int) ([]*Task, error) {
	//获取科目切割信息
	subjectinfolist, err := getCutSubjectAllInfo(subject)
	if err != nil {
		return nil, fmt.Errorf("获取科目信息失败:%v", err)
	}

	if len(subjectinfolist) == 0 {
		return nil, nil
	}

	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectinfolist[0].SubjectId)
	params["cutStatus"] = "1"
	var cutTaskList []*Task
	cutTaskList, err = job.FetchData[[]*Task, []*Task](
		job.NewHttpClient(),
		job.POST,
		"/cut/image/getList",
		params,
		nil)

	if err != nil {
		return nil, err
	}
	// 创建随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	cutTmpDirString := r.Intn(1000000)

	for _, task := range cutTaskList {
		task.CutTmpDir = config.BaseConfig.IniConfig.CutImageTempDir + "\\" + strconv.Itoa(cutTmpDirString)
		task.SubjectInfo = *subjectinfolist[0]
		//获取该考生已切割记录
		itemCutRecord, err := getItemCutRecord(task.SubjectId, task.CandidateNo)
		if err != nil {
			task.TaskError = fmt.Errorf("查询考生已切割记录失败: %v", err)
			continue
		}

		if len(itemCutRecord) == len(task.SubjectInfo.StartItemList) {
			task.TaskError = fmt.Errorf("该考生所有题均有切割记录，请设置重切后再切割！")
		}

		task.ItemCutRecord = itemCutRecord
		//获取考生选做题识别总串
		var optionalOmr string
		for _, image := range task.ImageList {
			if optionalOmr == "" {
				if image.FSubjectiveOmr != "" {
					optionalOmr = image.FSubjectiveOmr
					if image.BSubjectiveOmr != "" {
						optionalOmr = optionalOmr + "," + image.BSubjectiveOmr
					}
				} else {
					if image.BSubjectiveOmr != "" {
						optionalOmr = image.BSubjectiveOmr
					}
				}
			} else {
				if image.FSubjectiveOmr != "" {
					optionalOmr = optionalOmr + "," + image.FSubjectiveOmr
				}
				if image.BSubjectiveOmr != "" {
					optionalOmr = optionalOmr + "," + image.BSubjectiveOmr
				}
			}
		}
		task.OptionalSubjectOmr = optionalOmr
	}

	//准备切割前数据
	err = PrepareCutData(cutTaskList)
	if err != nil {
		return nil, fmt.Errorf("准备任务切割数据失败:%v", err)
	}
	return cutTaskList, nil
}

// ItemCutRecordJson 切割记录
type ItemCutRecordJson struct {
	ItemId    int    `json:"itemId"`
	CutStatus string `json:"cutStatus"`
	Version   int    `json:"version"`
}

// GetItemCutRecord 获取考生已切割记录
func getItemCutRecord(subjectId int, candidateNo string) ([]ItemCutRecordJson, error) {
	params := make(map[string]string)
	params["subjectId"] = strconv.Itoa(subjectId)
	params["candidateNo"] = candidateNo
	return job.FetchData[[]ItemCutRecordJson, []ItemCutRecordJson](
		job.NewHttpClient(),
		job.POST,
		"/candidate/item/cut/list",
		params,
		nil)
}

// PrepareCutData 准备切割数据
func PrepareCutData(tasklist []*Task) error {
	for _, task := range tasklist {
		if len(task.ImageList) > task.SubjectInfo.MaxCardModelCnt {
			task.TaskError = fmt.Errorf("考生%s题卡数为:%d张,大于模板最大题卡数:%d张",
				task.CandidateNo, len(task.ImageList), task.SubjectInfo.MaxCardModelCnt)
			continue
		}

		task.GenerateCutData()
	}
	return nil
}

func (ct *Task) updateTask() error {
	var updateList []TaskUpdate
	updateList = append(updateList, ct.TaskUpdate)

	httpClient := job.NewHttpClient()
	_, err := job.FetchData[string, string](
		httpClient,
		job.PostBody,
		"/candidate/item/cut/batchInsert",
		nil,
		updateList)

	if err != nil {
		return err
	}

	if httpClient.HttpClient.Code != 200 {
		return fmt.Errorf("更新数据失败:%d %s", httpClient.HttpClient.Code, httpClient.HttpClient.Msg)
	}

	return nil
}

// PrioritySignal 优先级信号
type PrioritySignal struct {
	HasPriority chan bool
	Stop        chan bool
}

// Cut 切割处理流程 - 支持优先级中断
func Cut(subject int, prioritySignal *PrioritySignal) error {
	getTaskErrCnt := 0
	continueTaskErrCnt := 0

	for {
		// 检查是否需要停止处理当前科目
		select {
		case <-prioritySignal.Stop:
			fmt.Printf("收到停止信号，科目 %d 处理中断\n", subject)
			return nil
		default:
		}

		// 单协程获取任务
		cutTaskList, err := GetCutTask(subject)
		if err != nil {
			fmt.Printf("获取切割数据失败:%v\n", err)
			getTaskErrCnt++
			if getTaskErrCnt > 5 {
				break
			} else {
				time.Sleep(time.Second * 2)
				continue
			}
		}

		if len(cutTaskList) == 0 {
			break
		}

		fmt.Printf("科目 %d 获取到 %d 个切割任务\n", subject, len(cutTaskList))
		for _, task := range cutTaskList {
			fmt.Printf("任务: 考生号=%s, 科目ID=%d\n", task.CandidateNo, task.SubjectId)
		}

		// 使用管道控制任务流程
		taskChan := make(chan *Task, len(cutTaskList))
		downloadChan := make(chan *Task, len(cutTaskList))
		processChan := make(chan *Task, len(cutTaskList))
		uploadChan := make(chan *Task, len(cutTaskList))
		updateChan := make(chan *Task, len(cutTaskList))

		// 将任务放入管道
		for _, task := range cutTaskList {
			taskChan <- task
		}
		close(taskChan)

		var wg sync.WaitGroup

		// 阶段1: 多协程下载图像
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer close(downloadChan)

			var downloadWg sync.WaitGroup
			// 限制并发下载数量
			semaphore := make(chan struct{}, 5)

			for task := range taskChan {
				downloadWg.Add(1)
				go func(t *Task) {
					defer downloadWg.Done()
					semaphore <- struct{}{}
					defer func() { <-semaphore }()

					if t.TaskError == nil {
						t.DownLoadWithRetry()
					}
					downloadChan <- t
				}(task)
			}
			downloadWg.Wait()
		}()

		// 阶段2: 多协程处理图像
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer close(processChan)

			var processWg sync.WaitGroup
			// 限制并发处理数量
			semaphore := make(chan struct{}, 15)

			for task := range downloadChan {
				processWg.Add(1)
				go func(t *Task) {
					defer processWg.Done()
					semaphore <- struct{}{}
					defer func() { <-semaphore }()

					if t.TaskError == nil {
						err := cgo.ImgCut(t.CutJson)
						if err != nil {
							t.TaskError = err
						}
					}
					processChan <- t
				}(task)
			}
			processWg.Wait()
		}()

		// 阶段3: 多协程上传图像
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer close(uploadChan)

			var uploadWg sync.WaitGroup
			// 限制并发上传数量
			semaphore := make(chan struct{}, 5)

			for task := range processChan {
				uploadWg.Add(1)
				go func(t *Task) {
					defer uploadWg.Done()
					semaphore <- struct{}{}
					defer func() { <-semaphore }()

					if t.TaskError == nil {
						t.UpLoadWithRetry()
					}
					uploadChan <- t
				}(task)
			}
			uploadWg.Wait()
		}()

		// 阶段4: 单协程更新状态
		wg.Add(1)
		go func() {
			defer wg.Done()
			defer close(updateChan)

			for task := range uploadChan {
				// 设置任务状态
				if task.TaskError != nil {
					task.TaskUpdate.CandidateCutUpdate.CutErrmsg = task.TaskError.Error()
					task.TaskUpdate.CandidateCutUpdate.CutStatus = "-1"
					task.TaskUpdate.CandidateItemCutList = []CandidateItemCut{}
					continueTaskErrCnt++
				} else {
					task.TaskUpdate.CandidateCutUpdate.CutErrmsg = ""
					task.TaskUpdate.CandidateCutUpdate.CutStatus = "8"
					continueTaskErrCnt = 0
				}

				// 单协程更新状态
				err := task.updateTask()
				if err != nil {
					fmt.Printf("更新任务状态失败: %v\n", err)
				}
				updateChan <- task
			}
		}()

		// 等待所有阶段完成
		wg.Wait()

		// 清理临时目录
		if len(cutTaskList) > 0 {
			err = os.RemoveAll(cutTaskList[0].CutTmpDir)
			if err != nil {
				fmt.Printf("删除临时目录失败: %v\n", err)
			}
		}

		//连续20次失败退出该科目任务
		if continueTaskErrCnt > 20 {
			return fmt.Errorf("连续20次任务处理失败，停止处理")
		}

		// 处理完当前批次任务后，检查是否有高优先级考试需要处理
		select {
		case hasPriority := <-prioritySignal.HasPriority:
			if hasPriority {
				fmt.Printf("检测到高优先级考试，科目 %d 暂停处理\n", subject)
				return nil
			}
		default:
			// 没有优先级信号，继续处理
		}
	}
	return nil
}

// Example 使用示例
func Example(subject int) {
	fmt.Printf("开始使用优化版本处理科目 %d 的切割任务\n", subject)

	// 创建优先级信号
	prioritySignal := &PrioritySignal{
		HasPriority: make(chan bool, 1),
		Stop:        make(chan bool, 1),
	}

	startTime := time.Now()
	err := Cut(subject, prioritySignal)
	duration := time.Since(startTime)

	if err != nil {
		fmt.Printf("优化版本处理失败: %v\n", err)
	} else {
		fmt.Printf("优化版本处理完成，耗时: %v\n", duration)
	}
}
