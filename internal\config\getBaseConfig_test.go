package config

import (
	"log"
	"testing"
)

func TestGetConfig(t *testing.T) {

	err := LoadConfig()

	if err != nil {
		log.Fatal(err)
	}
	log.Println("Type:", BaseConfig.IniConfig.AppHost.Type, "ip:", BaseConfig.IniConfig.AppHost.Host, "port:",
		BaseConfig.IniConfig.AppHost.Port, "MacAddress:", BaseConfig.MacAddress, "ExecDir:", BaseConfig.ExecDir, "TempDir:", BaseConfig.IniConfig.TempDir)
	log.Println("url:", BaseConfig.ServerUrl)
	log.Println("ExecDir:", BaseConfig.ExecDir)
	log.Println("CutImageTempDir:", BaseConfig.IniConfig.CutImageTempDir)
}
