package middleware

import (
	"log"
	"testing"
)

func Test_downloadFile(t *testing.T) {
	fileService := NewApacheFileClient("http://10.5.4.22:20501")

	remoteFile := "http://10.5.4.22:20501//ScanImg/sasdfasdfasdf_1004522//0//0425_226000019//00000003.Tif"
	//remoteFile := "http://127.0.0.1:8888//ScanImg/sasdfasdfasdf_1004522//0//0425_226000019//00000003.Tif"
	localFile := "F:\\test\\00000003.Tif"
	err := fileService.DownloadFile(remoteFile, localFile)
	if err != nil {
		log.Fatal(err)
	}

}

func Test_uploadFile(t *testing.T) {
	//remoteFile := "http://10.5.4.22:20501/file_uploadfile"
	//remoteFile := "http://127.0.0.1:8888/file_uploadfile"

	fileService := NewApacheFileClient("http://10.5.4.22:20501/file_uploadfile")
	//fileService := NewApacheFileService("http://127.0.0.1:8888/file_uploadfile")
	localFilePath := "F:\\test\\00000003.Tif"
	remoteFilePath := "aaaaaaa\\CutImg"

	err := fileService.UploadFile(localFilePath, remoteFilePath)
	if err != nil {
		log.Fatal(err)
	}
}
