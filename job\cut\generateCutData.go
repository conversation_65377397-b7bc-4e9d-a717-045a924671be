package cut

import (
	"fmt"
	"imgProcServe/cgo"
	"imgProcServe/job"
	"os"
	"path/filepath"
	"strconv"
	"strings"
)

// GenerateCutData 专卡切割数据
// 处理专用答题卡的切割数据生成
func (ct *Task) GenerateCutData() {
	if ct.TaskError == nil {
		// 准备输入图像数据
		ct.prepareDllInImageData()
	}

	if ct.TaskError == nil {
		//准备输出图像数据
		ct.prepareDllOutImageData()
	}

	//准备更新库数据
	ct.prepareTaskUpdateData()
}

// 准备考生原图数据：1.动态库输入图像数据 2.考生原图下载数据
func (ct *Task) prepareDllInImageData() {
	if ct.TaskError != nil {
		return
	}

	ct.CutJson.Func = "concat"
	ct.CutJson.RatioTh = ct.SubjectInfo.SubjectConfig.CutRatioTh

	// 预分配切片容量
	var imageCnt int

	//异常卡、通卡使用所有图像
	if ct.AbnormalCardFlag == "Y" {
		for _, image := range ct.ImageList {

			if image.FrontOcclusionArea == "" && image.BackOcclusionArea == "" &&
				image.FrontOcclusionRotate == "" && image.BackOcclusionRotate == "" {
				ct.TaskError = fmt.Errorf("考生为异常卡，请在<<异常卡处理>>中确认图像后再切割！")
				return
			}

			if image.FImage != "" {
				imageCnt++
			}
			if image.BImage != "" {
				imageCnt++
			}
		}
	} else {
		//专卡仅使用开启切割的图像
		imageCnt = len(ct.SubjectInfo.ModelImageInfo)
	}

	cutInImgList := make([]cgo.DllInImg, 0, imageCnt)
	taskImageDownLoadList := make([]ImageDownLoad, 0, imageCnt)

	// 按考生图像循环
	for _, image := range ct.ImageList {
		if image.FImage == "" {
			ct.TaskError = fmt.Errorf("未获取到考生正面图像数据")
			return
		}
		//异常卡循环考生所有图像
		if ct.AbnormalCardFlag == "Y" {
			cutInImg, imageDownLoad := ct.prepareInImageData(image, "F", ModelImageInfo{})
			if ct.TaskError != nil {
				return
			}
			taskImageDownLoadList = append(taskImageDownLoadList, imageDownLoad)
			cutInImgList = append(cutInImgList, cutInImg)

			if image.BImage != "" {
				cutInImg, imageDownLoad = ct.prepareInImageData(image, "B", ModelImageInfo{})
				if ct.TaskError != nil {
					return
				}
				taskImageDownLoadList = append(taskImageDownLoadList, imageDownLoad)
				cutInImgList = append(cutInImgList, cutInImg)
			}
		} else {
			//正常卡只循环开启切割对应的图像
			for _, modelImageInfo := range ct.SubjectInfo.ModelImageInfo {
				if image.CardType != modelImageInfo.OmrType[1:2] {
					continue
				}

				if modelImageInfo.OmrType[2:] == "F" {
					cutInImg, imageDownLoad := ct.prepareInImageData(image, "F", modelImageInfo)
					if ct.TaskError != nil {
						return
					}
					taskImageDownLoadList = append(taskImageDownLoadList, imageDownLoad)
					cutInImgList = append(cutInImgList, cutInImg)
				}

				if modelImageInfo.OmrType[2:] == "B" {
					if image.BImage != "" {
						cutInImg, imageDownLoad := ct.prepareInImageData(image, "B", modelImageInfo)
						if ct.TaskError != nil {
							return
						}
						taskImageDownLoadList = append(taskImageDownLoadList, imageDownLoad)
						cutInImgList = append(cutInImgList, cutInImg)
					}
				}
			}
		}
	}
	ct.CutJson.InImgs = cutInImgList
	ct.ImageDownLoad = taskImageDownLoadList
	return
}

// 输出动态库输入图像信息、图像下载信息
func (ct *Task) prepareInImageData(image TaskImage, frontOrBack string, modelImageInfo ModelImageInfo) (cgo.DllInImg, ImageDownLoad) {

	//提交切割动态库原图信息Json
	var cutInImg cgo.DllInImg
	//考生原图下载Json
	var imageDownLoad ImageDownLoad
	//当前处理图像
	var currentImagePath string
	//当前处理图像遮挡区域
	var currentImageOcclusionArea string
	//当前处理图像旋转角度
	var currentImageRotate string
	var convertAngleErr error

	if ct.TaskError != nil {
		return cgo.DllInImg{}, ImageDownLoad{}
	}

	imageDownLoad.PreHttpPath = ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
	imageDownLoad.CardType = image.CardType + frontOrBack
	if frontOrBack == "F" {
		currentImagePath = image.FImage
		currentImageOcclusionArea = image.FrontOcclusionArea
		if ct.AbnormalCardFlag == "Y" {
			currentImageRotate, convertAngleErr = convertAngle(image.FrontOcclusionRotate, false)

		} else {
			currentImageRotate = modelImageInfo.Angle
		}

	} else {
		currentImagePath = image.BImage
		currentImageOcclusionArea = image.BackOcclusionArea
		if ct.AbnormalCardFlag == "Y" {
			currentImageRotate, convertAngleErr = convertAngle(image.BackOcclusionRotate, false)

		} else {
			currentImageRotate = modelImageInfo.Angle
		}
	}
	if convertAngleErr != nil {
		ct.TaskError = convertAngleErr
		return cutInImg, imageDownLoad
	}
	//如果在文件服务器本地，使用服务器本地路径
	if ct.SubjectInfo.SubjectConfig.IfScanFileServer {
		imageDownLoad.ImageSourcePath = ct.SubjectInfo.SubjectConfig.ScanImgLocalOrSharePath +
			strings.ReplaceAll(currentImagePath, "/", "\\")
	}
	imageDownLoad.ImageHttpPath = currentImagePath
	//图像下载到本地路径
	imageDownLoad.ImageDownLoadPath = ct.CutTmpDir + strings.ReplaceAll(currentImagePath, "/", "\\")
	imageDownLoad.ImageDownLoadPath = strings.ReplaceAll(imageDownLoad.ImageDownLoadPath, "\\", "\\\\")

	cutInImg.RotateAngle, _ = strconv.Atoi(currentImageRotate)

	if ct.AbnormalCardFlag != "Y" {
		//异常题卡不传模版宽高
		cutInImg.ModelW, _ = strconv.Atoi(modelImageInfo.ModWidth)
		cutInImg.ModelH, _ = strconv.Atoi(modelImageInfo.ModHeight)
		//遮挡区域校正
		if currentImageOcclusionArea != "" {
			cutInImg.OcclusionAreaRects = reviseOcclusionRect(stringConvertOcclusionArea(
				currentImageOcclusionArea),
				imageDownLoad.CardType,
				ct.ImageList,
				ct.SubjectInfo.ModelImageInfo)
		}
	}

	cutInImg.ImagePath = imageDownLoad.ImageDownLoadPath
	return cutInImg, imageDownLoad
}

// 生成动态库OutImgs信息，切割图像上传信息
func (ct *Task) prepareDllOutImageData() {
	if ct.AbnormalCardFlag == "Y" {
		ct.prepareDllOutImageDataByImage()
		return
	}

	if ct.SubjectInfo.GeneralCardFlag == "1" {
		ct.prepareDllOutImageDataByCutRect()
	} else {
		ct.prepareDllOutImageDataByItem()
	}
}

// 异常卡按考生原图生成动态库OutImgs信息，切割图像上传信息
func (ct *Task) prepareDllOutImageDataByImage() {
	firstItem := ct.SubjectInfo.StartItemList[0]
	imageCnt := 1
	for _, image := range ct.ImageDownLoad {
		var cutOutImg cgo.DllOutImg
		var cutOutInImg cgo.DllOutInImg

		if firstItem.Stitich == 0 || ct.AbnormalCardFlag == "Y" {
			cutOutImg.HorV = "V"
		} else {
			cutOutImg.HorV = "H"
		}
		cutOutImg.Align = 1
		cutOutImg.ImgUrl = getCutOutFilePath(
			job.DBID,
			ct.SubjectId,
			ct.OrgId,
			ct.RoomEncode,
			imageCnt,
			ct.CandidateNo,
			firstItem.ImgType)

		if ct.SubjectInfo.SubjectConfig.IfPjFileServer {
			cutOutImg.OutImg = ct.SubjectInfo.SubjectConfig.PjImgLocalOrSharePath + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		} else {
			cutOutImg.OutImg = ct.CutTmpDir + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		}
		cutOutImg.OutImg = strings.ReplaceAll(cutOutImg.OutImg, "\\", "\\\\")

		// 确保本地目录存在
		localDir := filepath.Dir(cutOutImg.OutImg)
		if err := os.MkdirAll(localDir, 0755); err != nil {
			ct.TaskError = fmt.Errorf("创建切割图像保存目录失败: %w", err)
			return
		}

		cutOutInImg.ImagePath = image.ImageDownLoadPath
		cutOutImg.InImgs = append(cutOutImg.InImgs, cutOutInImg)
		ct.CutJson.OutImgs = append(ct.CutJson.OutImgs, cutOutImg)

		//切割子图上传信息
		var cutImageUpLoad ImageUpLoad
		cutImageUpLoad.UpLoadImageSourcePath = cutOutImg.OutImg
		cutImageUpLoad.ImageHttpPath = cutOutImg.ImgUrl
		cutImageUpLoad.PreHttpPath = ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
		ct.ImageUpLoad = append(ct.ImageUpLoad, cutImageUpLoad)
	}
}

// 通卡按切割块生成动态库OutImgs信息，切割图像上传信息
func (ct *Task) prepareDllOutImageDataByCutRect() {

	firstItem := ct.SubjectInfo.StartItemList[0]
	for _, rect := range firstItem.CutRectList {
		var cutOutImg cgo.DllOutImg
		var cutOutInImg cgo.DllOutInImg

		if firstItem.Stitich == 0 || ct.AbnormalCardFlag == "Y" {
			cutOutImg.HorV = "V"
		} else {
			cutOutImg.HorV = "H"
		}
		cutOutImg.Align = 1

		cutOutImg.ImgUrl = getCutOutFilePath(
			job.DBID,
			ct.SubjectId,
			ct.OrgId,
			ct.RoomEncode,
			rect.ItemCutId,
			ct.CandidateNo,
			firstItem.ImgType)

		if ct.SubjectInfo.SubjectConfig.IfPjFileServer {
			cutOutImg.OutImg = ct.SubjectInfo.SubjectConfig.PjImgLocalOrSharePath + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		} else {
			cutOutImg.OutImg = ct.CutTmpDir + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		}
		cutOutImg.OutImg = strings.ReplaceAll(cutOutImg.OutImg, "\\", "\\\\")

		// 确保本地目录存在
		localDir := filepath.Dir(cutOutImg.OutImg)
		if err := os.MkdirAll(localDir, 0755); err != nil {
			ct.TaskError = fmt.Errorf("创建切割图像保存目录失败: %w", err)
			return
		}

		//切割原图下载路径
		for _, downLoadImage := range ct.ImageDownLoad {
			if rect.ImageType[1:] == downLoadImage.CardType {
				cutOutInImg.ImagePath = downLoadImage.ImageDownLoadPath
			}
		}

		//切割坐标校正
		reviseRect := reviseCutRect(rect, ct.ImageList, ct.SubjectInfo.ModelImageInfo)
		//切割区域
		h, _ := strconv.Atoi(reviseRect.RectHeight)
		w, _ := strconv.Atoi(reviseRect.RectWidth)
		x, _ := strconv.Atoi(reviseRect.RectLeft)
		y, _ := strconv.Atoi(reviseRect.RectTop)

		cutOutInImg.Rects = append(cutOutInImg.Rects, cgo.DllCutArea{Height: h, Width: w, PosX: x, PosY: y})
		cutOutImg.InImgs = append(cutOutImg.InImgs, cutOutInImg)
		ct.CutJson.OutImgs = append(ct.CutJson.OutImgs, cutOutImg)

		//切割子图上传信息
		var cutImageUpLoad ImageUpLoad
		cutImageUpLoad.UpLoadImageSourcePath = cutOutImg.OutImg
		cutImageUpLoad.ImageHttpPath = cutOutImg.ImgUrl
		cutImageUpLoad.PreHttpPath = ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
		ct.ImageUpLoad = append(ct.ImageUpLoad, cutImageUpLoad)
	}
}

// 专卡按题生成动态库OutImgs信息，切割图像上传信息
func (ct *Task) prepareDllOutImageDataByItem() {

ItemLoop:
	for _, item := range ct.SubjectInfo.StartItemList {
		//如果已切割或已重切，跳过到下一道题
		for _, itemCutRecord := range ct.ItemCutRecord {
			if item.ItemId == itemCutRecord.ItemId &&
				(itemCutRecord.CutStatus == "1" || itemCutRecord.CutStatus == "3") {
				continue ItemLoop
			}
		}

		var cutOutImg cgo.DllOutImg

		if item.Stitich == 0 || ct.AbnormalCardFlag == "Y" {
			cutOutImg.HorV = "V"
		} else {
			cutOutImg.HorV = "H"
		}
		cutOutImg.Align = 1

		cutOutImg.ImgUrl = getCutOutFilePath(
			job.DBID,
			ct.SubjectId,
			ct.OrgId,
			ct.RoomEncode,
			item.ItemId,
			ct.CandidateNo,
			item.ImgType)

		if ct.SubjectInfo.SubjectConfig.IfPjFileServer {
			cutOutImg.OutImg = ct.SubjectInfo.SubjectConfig.PjImgLocalOrSharePath + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		} else {
			cutOutImg.OutImg = ct.CutTmpDir + strings.ReplaceAll(cutOutImg.ImgUrl, "/", "\\")
		}
		cutOutImg.OutImg = strings.ReplaceAll(cutOutImg.OutImg, "\\", "\\\\")
		// 确保本地目录存在
		localDir := filepath.Dir(cutOutImg.OutImg)
		if err := os.MkdirAll(localDir, 0755); err != nil {
			ct.TaskError = fmt.Errorf("创建切割图像保存目录失败: %w", err)
			return
		}

		for _, cutRect := range item.CutRectList {
			//切割原图下载路径
			var cutOutInImg cgo.DllOutInImg
			for _, downLoadImage := range ct.ImageDownLoad {
				if cutRect.ImageType[1:] == downLoadImage.CardType {
					cutOutInImg.ImagePath = downLoadImage.ImageDownLoadPath
				}
			}

			//切割坐标校正
			reviseRect := reviseCutRect(cutRect, ct.ImageList, ct.SubjectInfo.ModelImageInfo)
			//切割区域
			h, _ := strconv.Atoi(reviseRect.RectHeight)
			w, _ := strconv.Atoi(reviseRect.RectWidth)
			x, _ := strconv.Atoi(reviseRect.RectLeft)
			y, _ := strconv.Atoi(reviseRect.RectTop)

			cutOutInImg.Rects = append(cutOutInImg.Rects, cgo.DllCutArea{Height: h, Width: w, PosX: x, PosY: y})
			cutOutImg.InImgs = append(cutOutImg.InImgs, cutOutInImg)
		}
		ct.CutJson.OutImgs = append(ct.CutJson.OutImgs, cutOutImg)

		//切割子图上传信息
		var cutImageUpLoad ImageUpLoad
		cutImageUpLoad.UpLoadImageSourcePath = cutOutImg.OutImg
		cutImageUpLoad.ImageHttpPath = cutOutImg.ImgUrl
		cutImageUpLoad.PreHttpPath = ct.SubjectInfo.SubjectConfig.ImgUpDownLoadHeader
		ct.ImageUpLoad = append(ct.ImageUpLoad, cutImageUpLoad)
	}
}

// 准备更新库数据
func (ct *Task) prepareTaskUpdateData() {
	if ct.TaskError == nil {
	ItemLoop:
		for _, item := range ct.SubjectInfo.StartItemList {
			//如果已切割或已重切，跳过到下一道题
			for _, itemCutRecord := range ct.ItemCutRecord {
				if item.ItemId == itemCutRecord.ItemId &&
					(itemCutRecord.CutStatus == "1" || itemCutRecord.CutStatus == "3") {
					continue ItemLoop
				}
			}

			//更新库t_candidate_item_cut信息
			var itemCutUpdate CandidateItemCut
			itemCutUpdate.SubjectId = strconv.Itoa(ct.SubjectId)
			itemCutUpdate.ItemId = strconv.Itoa(item.ItemId)
			itemCutUpdate.CandidateNo = ct.CandidateNo
			itemCutUpdate.CutImgServerIp = ct.SubjectInfo.SubjectConfig.ImgBrowseHttpHeader
			if ct.AbnormalCardFlag == "N" && ct.SubjectInfo.GeneralCardFlag == "0" {
				itemCutUpdate.Url =
					getCutOutFilePath(
						job.DBID,
						ct.SubjectId,
						ct.OrgId,
						ct.RoomEncode,
						item.ItemId,
						ct.CandidateNo,
						item.ImgType)
			} else {
				for _, outImg := range ct.CutJson.OutImgs {
					if itemCutUpdate.Url == "" {
						itemCutUpdate.Url = outImg.ImgUrl
					} else {
						itemCutUpdate.Url = itemCutUpdate.Url + ";" + outImg.ImgUrl
					}
				}
			}
			//选做题
			if item.JudgeFlag == "1" {
				itemCutUpdate.JudgeItem = "1"
				itemCutUpdate.JudgeItemId = strconv.Itoa(item.ItemId)
				if item.OptSelectHasOmr == "Y" {
					//获取考生选做题识别结果
					var getOptSelectOmrResultError error
					itemCutUpdate.OptSelectOmrResult, getOptSelectOmrResultError = getOptionRecogItem(
						ct.SubjectInfo.OptItemNo,
						item.OptSelectItemStr,
						item.OptSelectItemType,
						ct.OptionalSubjectOmr)
					if getOptSelectOmrResultError != nil {
						ct.TaskError = getOptSelectOmrResultError
						continue ItemLoop
					}
				} else {
					//如果无选做题识别点，则直接生成判别任务
					itemCutUpdate.OptSelectOmrResult = "-1"
				}
			}

			//切割方案mmd5串
			for _, cutRect := range item.CutRectList {
				//切割方案mmd5串
				if itemCutUpdate.CutMd5 == "" {
					itemCutUpdate.CutMd5 = cutRect.ImgCutMD5
				} else {
					itemCutUpdate.CutMd5 = itemCutUpdate.CutMd5 + ";" + cutRect.ImgCutMD5
				}
			}
			//如果有切割记录，则传重切记录版本号
			if len(ct.ItemCutRecord) > 0 {
				itemCutUpdate.Version = strconv.Itoa(ct.ItemCutRecord[0].Version)
			} else {
				itemCutUpdate.Version = "0"
			}
			itemCutUpdate.CutStatus = "1"
			ct.TaskUpdate.CandidateItemCutList = append(ct.TaskUpdate.CandidateItemCutList, itemCutUpdate)
		}
	}

	ct.TaskUpdate.CandidateCutUpdate.SubjectId = strconv.Itoa(ct.SubjectId)
	ct.TaskUpdate.CandidateCutUpdate.CandidateNo = ct.CandidateNo
	ct.TaskUpdate.CandidateCutUpdate.ScanBatchNo = ct.ScanBatchNO
	ct.TaskUpdate.CandidateCutUpdate.Version = strconv.Itoa(ct.Version)

	if ct.TaskError != nil {
		ct.TaskUpdate.CandidateCutUpdate.CutStatus = "-1"
		ct.TaskUpdate.CandidateItemCutList = []CandidateItemCut{}
	} else {
		ct.TaskUpdate.CandidateCutUpdate.CutStatus = "1"
	}
}
