package cgo

/*
#cgo LDFLAGS: -L${SRCDIR}/../lib/windows/amd64 -lSeaAirOMR
#include "../lib/include/SeaAirOMR.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
*/
import "C"
import (
	"encoding/json"
	"fmt"
	"log"
	"unsafe"
)

// ModelFileArr 模板路径
type ModelFileArr struct {
	ModelFilePath string `json:"subjectModelFile"`
}

// ModelIniJson 模板初始化Json
type ModelIniJson struct {
	Function       string         `json:"func"`
	ModelFilePaths []ModelFileArr `json:"subjectModels"`
}

// ModelInit 模板初始化
func ModelInit(filePath string) error {
	modelPath := ModelFileArr{filePath}
	var modelPaths []ModelFileArr
	modelPaths = append(modelPaths, modelPath)

	modelJson := ModelIniJson{
		Function:       "AnsCardRecIni",
		ModelFilePaths: modelPaths,
	}

	jsonData, err := json.Marshal(modelJson)
	if err != nil {
		return fmt.Errorf("转换模板初始化Json失败: %v", err)
	}
	cInString := C.CString(string(jsonData))
	defer C.free(unsafe.Pointer(cInString))

	// Go 变量，类型为 C 指针，初始值为 nil
	var cOutString *C.char
	defer C.free(unsafe.Pointer(cOutString))
	result, err := C.SeaAirOMRGo(cInString, &cOutString)

	if err != nil {
		return fmt.Errorf("初始化模版失败: %v", err)
	}

	// 检查返回值
	if int(result) != 0 {
		return fmt.Errorf("初始化模版失败，错误代码: %v", int(result))
	} else {
		log.Printf("初始化模版成功: %v", int(result))
	}
	return nil
}

// CardRecogniseItemsJson 题卡识别内容开关
type CardRecogniseItemsJson struct {
	CandidateNumberItem bool `json:"zkzhItems"`   //考号
	KgtOmrArea          bool `json:"kgtOmrAreas"` //客观题
	AbsentFlag          bool `json:"absentFlag"`  //缺考点
	ViolateFlag         bool `json:"violateFlag"` //违纪
	CourseType          bool `json:"courseType"`  //科类代码
	ZgtOmrAreas         bool `json:"zgtOmrAreas"` //主观题选做题OMR
}

// CardRecogniseJson 题卡识别Json
type CardRecogniseJson struct {
	Function         string                 `json:"func"`
	FrontImagePath   string                 `json:"InImgF"`
	BackImagePath    string                 `json:"InImgB"`
	RecognitionItems CardRecogniseItemsJson `json:"recItem"`
}

// RecogniseCard 题卡识别
func RecogniseCard(front, back string) (string, error) {
	//识别项
	cardRecogniseItems := CardRecogniseItemsJson{
		CandidateNumberItem: true,
		KgtOmrArea:          true,
		AbsentFlag:          true,
		ViolateFlag:         true,
		CourseType:          true,
		ZgtOmrAreas:         true,
	}
	//识别json
	recogniseJson := CardRecogniseJson{
		Function:         "AnsCardRec",
		FrontImagePath:   front,
		BackImagePath:    back,
		RecognitionItems: cardRecogniseItems,
	}

	recogniseJsonData, err := json.Marshal(recogniseJson)
	if err != nil {

		return "", fmt.Errorf("转换识别Json失败：%w", err)
	}
	recogniseInString := C.CString(string(recogniseJsonData))
	defer C.free(unsafe.Pointer(recogniseInString))

	// Go 变量，类型为 C 指针，初始值为 nil
	var recogniseOutString *C.char
	defer C.free(unsafe.Pointer(recogniseOutString))
	recogniseResult, err := C.SeaAirOMRGo(recogniseInString, &recogniseOutString)

	if err != nil {
		return "", fmt.Errorf("识别失败: %w", err)
	}

	if int(recogniseResult) != 0 {

		return "", fmt.Errorf("识别失败，错误代码: %d", int(recogniseResult))
	} else {
		log.Printf("识别成功: %v", int(recogniseResult))
	}

	// 转换为 Go 字符串
	goString := C.GoString(recogniseOutString)
	return goString, nil
}
